<?php
/**
 * Validation Script for Automatic Subnet Allocation System
 * 
 * This script performs read-only validation checks to ensure the automatic
 * subnet allocation system is properly configured and ready for use.
 * 
 * Usage: php validate_subnet_allocation_system.php
 * 
 * This is safe to run in production as it only performs read operations.
 */

require_once("config.php");

class SubnetAllocationValidator {
    private $pdo;
    private $validationResults = [];
    private $warnings = [];
    private $errors = [];

    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    /**
     * Run all validation checks
     */
    public function runValidation() {
        $this->log("=== Automatic Subnet Allocation System Validation ===\n");
        
        $this->validateDatabaseTables();
        $this->validateFunctions();
        $this->validateAPIEndpoints();
        $this->validateSubnetAvailability();
        $this->validateServerInventory();
        $this->validateConfiguration();
        $this->validateLoggingSystem();
        
        $this->reportResults();
        
        return count($this->errors) === 0;
    }

    /**
     * Validate required database tables exist
     */
    private function validateDatabaseTables() {
        $this->log("--- Validating Database Tables ---");
        
        $requiredTables = [
            'subnets' => ['id', 'subnet', 'subnet_size', 'assigned_server_id', 'is_allocated', 'is_customer_subnet', 'is_public'],
            'inventory_dedicated_servers' => ['id', 'main_ip', 'additional_ips', 'city_id'],
            'blade_server_inventory' => ['id', 'main_ip', 'additional_ips', 'city_id'],
            'subnet_allocation_log' => ['id', 'server_id', 'server_type', 'subnet_id', 'status', 'created_at'],
            'cities' => ['id', 'city', 'datacenter'],
            'subnet_location_map' => ['subnet_id', 'city_id']
        ];
        
        foreach ($requiredTables as $tableName => $requiredColumns) {
            try {
                // Check if table exists
                $stmt = $this->pdo->query("SHOW TABLES LIKE '$tableName'");
                if ($stmt->rowCount() === 0) {
                    $this->errors[] = "Required table '$tableName' does not exist";
                    continue;
                }
                
                // Check required columns
                $stmt = $this->pdo->query("DESCRIBE $tableName");
                $existingColumns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');
                
                $missingColumns = array_diff($requiredColumns, $existingColumns);
                if (!empty($missingColumns)) {
                    $this->errors[] = "Table '$tableName' missing columns: " . implode(', ', $missingColumns);
                } else {
                    $this->validationResults['tables'][$tableName] = 'OK';
                    $this->log("✓ Table '$tableName' structure valid");
                }
                
            } catch (Exception $e) {
                $this->errors[] = "Error validating table '$tableName': " . $e->getMessage();
            }
        }
    }

    /**
     * Validate required functions exist
     */
    private function validateFunctions() {
        $this->log("\n--- Validating Functions ---");
        
        // Check if the functions are defined in the API file
        $apiFile = 'api_admin_subnets.php';
        if (!file_exists($apiFile)) {
            $this->errors[] = "API file '$apiFile' not found";
            return;
        }
        
        $apiContent = file_get_contents($apiFile);
        $requiredFunctions = [
            'autoAllocateSubnetToServer',
            'findBestAvailableSubnet',
            'deallocateServerSubnet',
            'allocateSubnetToServerInternal',
            'logSubnetAllocation',
            'getSubnetAllocationHistory',
            'getSubnetAllocationStats'
        ];
        
        foreach ($requiredFunctions as $functionName) {
            if (strpos($apiContent, "function $functionName") !== false) {
                $this->validationResults['functions'][$functionName] = 'OK';
                $this->log("✓ Function '$functionName' found");
            } else {
                $this->errors[] = "Required function '$functionName' not found in API file";
            }
        }
    }

    /**
     * Validate API endpoints
     */
    private function validateAPIEndpoints() {
        $this->log("\n--- Validating API Endpoints ---");
        
        $apiFile = 'api_admin_subnets.php';
        if (!file_exists($apiFile)) {
            return; // Already reported in validateFunctions
        }
        
        $apiContent = file_get_contents($apiFile);
        $requiredEndpoints = [
            'auto_allocate_subnet',
            'get_allocation_history',
            'get_allocation_stats'
        ];
        
        foreach ($requiredEndpoints as $endpoint) {
            if (strpos($apiContent, "f'] == '$endpoint'") !== false) {
                $this->validationResults['endpoints'][$endpoint] = 'OK';
                $this->log("✓ API endpoint '$endpoint' found");
            } else {
                $this->errors[] = "Required API endpoint '$endpoint' not found";
            }
        }
    }

    /**
     * Validate subnet availability
     */
    private function validateSubnetAvailability() {
        $this->log("\n--- Validating Subnet Availability ---");
        
        try {
            // Check for available customer subnets
            $stmt = $this->pdo->query("
                SELECT COUNT(*) as count FROM subnets 
                WHERE is_customer_subnet = 1 
                AND is_public = 1 
                AND is_managmenet_subnet = 0
                AND assigned_server_id IS NULL 
                AND is_allocated = 0
            ");
            $availableCount = $stmt->fetch()['count'];
            
            if ($availableCount > 0) {
                $this->validationResults['subnet_availability'] = 'OK';
                $this->log("✓ Found $availableCount available customer subnets");
            } else {
                $this->warnings[] = "No available customer subnets found - automatic allocation may fail";
                $this->log("⚠ No available customer subnets found");
            }
            
            // Check subnet size distribution
            $stmt = $this->pdo->query("
                SELECT subnet_size, COUNT(*) as count 
                FROM subnets 
                WHERE is_customer_subnet = 1 AND assigned_server_id IS NULL 
                GROUP BY subnet_size 
                ORDER BY subnet_size
            ");
            $sizeDistribution = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $this->log("Subnet size distribution:");
            foreach ($sizeDistribution as $size) {
                $this->log("  /{$size['subnet_size']}: {$size['count']} subnets");
            }
            
        } catch (Exception $e) {
            $this->errors[] = "Error validating subnet availability: " . $e->getMessage();
        }
    }

    /**
     * Validate server inventory
     */
    private function validateServerInventory() {
        $this->log("\n--- Validating Server Inventory ---");
        
        try {
            // Check dedicated servers
            $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM inventory_dedicated_servers");
            $dedicatedCount = $stmt->fetch()['count'];
            
            // Check blade servers
            $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM blade_server_inventory");
            $bladeCount = $stmt->fetch()['count'];
            
            if ($dedicatedCount > 0 || $bladeCount > 0) {
                $this->validationResults['server_inventory'] = 'OK';
                $this->log("✓ Server inventory: $dedicatedCount dedicated, $bladeCount blade servers");
            } else {
                $this->warnings[] = "No servers found in inventory";
                $this->log("⚠ No servers found in inventory");
            }
            
            // Check for servers with location information
            $stmt = $this->pdo->query("
                SELECT COUNT(*) as count FROM inventory_dedicated_servers 
                WHERE city_id IS NOT NULL
            ");
            $dedicatedWithLocation = $stmt->fetch()['count'];
            
            $stmt = $this->pdo->query("
                SELECT COUNT(*) as count FROM blade_server_inventory 
                WHERE city_id IS NOT NULL
            ");
            $bladeWithLocation = $stmt->fetch()['count'];
            
            $totalWithLocation = $dedicatedWithLocation + $bladeWithLocation;
            $totalServers = $dedicatedCount + $bladeCount;
            
            if ($totalWithLocation > 0) {
                $percentage = round(($totalWithLocation / $totalServers) * 100, 1);
                $this->log("✓ $totalWithLocation/$totalServers servers ($percentage%) have location information");
            } else {
                $this->warnings[] = "No servers have location information - location-based allocation will not work";
            }
            
        } catch (Exception $e) {
            $this->errors[] = "Error validating server inventory: " . $e->getMessage();
        }
    }

    /**
     * Validate system configuration
     */
    private function validateConfiguration() {
        $this->log("\n--- Validating Configuration ---");
        
        try {
            // Check if cities table has data
            $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM cities");
            $cityCount = $stmt->fetch()['count'];
            
            if ($cityCount > 0) {
                $this->validationResults['cities'] = 'OK';
                $this->log("✓ Found $cityCount cities in database");
            } else {
                $this->warnings[] = "No cities found - location-based allocation will not work";
            }
            
            // Check subnet-location mapping
            $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM subnet_location_map");
            $mappingCount = $stmt->fetch()['count'];
            
            if ($mappingCount > 0) {
                $this->validationResults['subnet_location_mapping'] = 'OK';
                $this->log("✓ Found $mappingCount subnet-location mappings");
            } else {
                $this->warnings[] = "No subnet-location mappings found - location preference may not work optimally";
            }
            
        } catch (Exception $e) {
            $this->errors[] = "Error validating configuration: " . $e->getMessage();
        }
    }

    /**
     * Validate logging system
     */
    private function validateLoggingSystem() {
        $this->log("\n--- Validating Logging System ---");
        
        try {
            // Check if logging table exists and is accessible
            $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM subnet_allocation_log");
            $logCount = $stmt->fetch()['count'];
            
            $this->validationResults['logging_system'] = 'OK';
            $this->log("✓ Logging system accessible, $logCount existing log entries");
            
            // Check for recent activity
            $stmt = $this->pdo->query("
                SELECT COUNT(*) as count FROM subnet_allocation_log 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            ");
            $recentCount = $stmt->fetch()['count'];
            
            if ($recentCount > 0) {
                $this->log("✓ Found $recentCount recent allocation log entries (last 30 days)");
            } else {
                $this->log("ℹ No recent allocation activity found");
            }
            
        } catch (Exception $e) {
            $this->errors[] = "Error validating logging system: " . $e->getMessage();
        }
    }

    /**
     * Report validation results
     */
    private function reportResults() {
        $this->log("\n=== VALIDATION RESULTS ===");
        
        // Count results
        $totalChecks = 0;
        $passedChecks = 0;
        
        foreach ($this->validationResults as $category => $results) {
            if (is_array($results)) {
                foreach ($results as $check => $result) {
                    $totalChecks++;
                    if ($result === 'OK') $passedChecks++;
                }
            } else {
                $totalChecks++;
                if ($results === 'OK') $passedChecks++;
            }
        }
        
        // Report summary
        $this->log("Validation Summary:");
        $this->log("- Passed: $passedChecks/$totalChecks checks");
        $this->log("- Errors: " . count($this->errors));
        $this->log("- Warnings: " . count($this->warnings));
        
        // Report errors
        if (!empty($this->errors)) {
            $this->log("\n❌ ERRORS (must be fixed):");
            foreach ($this->errors as $error) {
                $this->log("  • $error");
            }
        }
        
        // Report warnings
        if (!empty($this->warnings)) {
            $this->log("\n⚠️  WARNINGS (should be addressed):");
            foreach ($this->warnings as $warning) {
                $this->log("  • $warning");
            }
        }
        
        // Final status
        if (empty($this->errors)) {
            if (empty($this->warnings)) {
                $this->log("\n🎉 VALIDATION PASSED! System is ready for automatic subnet allocation.");
            } else {
                $this->log("\n✅ VALIDATION PASSED with warnings. System is functional but some optimizations are recommended.");
            }
        } else {
            $this->log("\n❌ VALIDATION FAILED! Please fix the errors before using automatic subnet allocation.");
        }
    }

    /**
     * Log message with timestamp
     */
    private function log($message) {
        echo "[" . date('Y-m-d H:i:s') . "] " . $message . "\n";
    }
}

// Run validation if script is executed directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    try {
        $validator = new SubnetAllocationValidator($pdo);
        $isValid = $validator->runValidation();
        exit($isValid ? 0 : 1);
    } catch (Exception $e) {
        echo "FATAL ERROR: " . $e->getMessage() . "\n";
        exit(1);
    }
}
?>
