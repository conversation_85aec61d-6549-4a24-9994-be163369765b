<?php
/**
 * Comprehensive Test Suite for Automatic Subnet Allocation System
 * 
 * This script validates the automatic subnet allocation functionality
 * to ensure it works correctly and doesn't cause conflicts or data integrity issues.
 * 
 * Usage: php test_automatic_subnet_allocation.php
 * 
 * IMPORTANT: This should be run in a test environment, not production!
 */

require_once("config.php");
require_once("auth_functions.php");

// Test configuration
$TEST_CONFIG = [
    'test_server_ids' => [
        'dedicated' => 1000001, // Test dedicated server ID
        'blade' => 999999      // Test blade server ID
    ],
    'test_location_id' => 1,
    'cleanup_after_tests' => true,
    'verbose_output' => true
];

class SubnetAllocationTester {
    private $pdo;
    private $config;
    private $testResults = [];
    private $testSubnets = [];
    private $originalServerStates = [];

    public function __construct($pdo, $config) {
        $this->pdo = $pdo;
        $this->config = $config;
    }

    /**
     * Run all tests
     */
    public function runAllTests() {
        $this->log("=== Starting Automatic Subnet Allocation Test Suite ===\n");
        
        try {
            // Setup test environment
            $this->setupTestEnvironment();
            
            // Run individual tests
            $this->testBasicAllocation();
            $this->testConflictResolution();
            $this->testServerTypeDetection();
            $this->testLocationPreference();
            $this->testErrorHandling();
            $this->testDatabaseIntegrity();
            $this->testLoggingFunctionality();
            
            // Cleanup
            if ($this->config['cleanup_after_tests']) {
                $this->cleanupTestEnvironment();
            }
            
            // Report results
            $this->reportResults();
            
        } catch (Exception $e) {
            $this->log("CRITICAL ERROR: " . $e->getMessage());
            $this->cleanupTestEnvironment();
            throw $e;
        }
    }

    /**
     * Setup test environment
     */
    private function setupTestEnvironment() {
        $this->log("Setting up test environment...");
        
        // Save original server states
        foreach ($this->config['test_server_ids'] as $type => $serverId) {
            $table = ($type === 'dedicated') ? 'inventory_dedicated_servers' : 'blade_server_inventory';
            $stmt = $this->pdo->prepare("SELECT main_ip, additional_ips FROM $table WHERE id = ?");
            $stmt->execute([$serverId]);
            $this->originalServerStates[$serverId] = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$this->originalServerStates[$serverId]) {
                throw new Exception("Test server $serverId ($type) not found in database");
            }
        }
        
        // Create test subnets if needed
        $this->createTestSubnets();
        
        $this->log("Test environment setup complete.");
    }

    /**
     * Create test subnets for allocation testing
     */
    private function createTestSubnets() {
        $testSubnets = [
            ['subnet' => '*************/29', 'size' => 29],
            ['subnet' => '*************/28', 'size' => 28],
            ['subnet' => '*************/30', 'size' => 30]
        ];
        
        foreach ($testSubnets as $subnetData) {
            // Check if subnet already exists
            $stmt = $this->pdo->prepare("SELECT id FROM subnets WHERE subnet = ?");
            $stmt->execute([$subnetData['subnet']]);
            
            if (!$stmt->fetch()) {
                // Create test subnet
                $insertStmt = $this->pdo->prepare("
                    INSERT INTO subnets (subnet, subnet_size, is_customer_subnet, is_public, is_managmenet_subnet, note)
                    VALUES (?, ?, 1, 1, 0, 'TEST SUBNET - AUTO ALLOCATION TEST')
                ");
                $insertStmt->execute([$subnetData['subnet'], $subnetData['size']]);
                $this->testSubnets[] = $this->pdo->lastInsertId();
                $this->log("Created test subnet: " . $subnetData['subnet']);
            }
        }
    }

    /**
     * Test basic allocation functionality
     */
    private function testBasicAllocation() {
        $this->log("\n--- Testing Basic Allocation ---");
        
        foreach ($this->config['test_server_ids'] as $type => $serverId) {
            try {
                $this->log("Testing allocation for $type server $serverId");
                
                // Make API call to auto allocate subnet
                $result = $this->callAutoAllocationAPI($serverId, $type);
                
                if ($result['success']) {
                    $this->testResults['basic_allocation'][$type] = 'PASS';
                    $this->log("✓ Basic allocation successful for $type server");
                    $this->log("  Allocated subnet: " . $result['subnet_cidr']);
                } else {
                    $this->testResults['basic_allocation'][$type] = 'FAIL';
                    $this->log("✗ Basic allocation failed for $type server: " . ($result['error'] ?? 'Unknown error'));
                }
                
            } catch (Exception $e) {
                $this->testResults['basic_allocation'][$type] = 'ERROR';
                $this->log("✗ Exception during basic allocation test: " . $e->getMessage());
            }
        }
    }

    /**
     * Test conflict resolution (deallocating existing subnets)
     */
    private function testConflictResolution() {
        $this->log("\n--- Testing Conflict Resolution ---");
        
        $serverId = $this->config['test_server_ids']['dedicated'];
        
        try {
            // First allocation
            $result1 = $this->callAutoAllocationAPI($serverId, 'dedicated');
            if (!$result1['success']) {
                throw new Exception("First allocation failed: " . ($result1['error'] ?? 'Unknown error'));
            }
            
            $firstSubnet = $result1['subnet_cidr'];
            $this->log("First allocation: $firstSubnet");
            
            // Second allocation (should deallocate first and allocate new)
            $result2 = $this->callAutoAllocationAPI($serverId, 'dedicated');
            if (!$result2['success']) {
                throw new Exception("Second allocation failed: " . ($result2['error'] ?? 'Unknown error'));
            }
            
            $secondSubnet = $result2['subnet_cidr'];
            $this->log("Second allocation: $secondSubnet");
            
            // Verify old subnet is deallocated
            $stmt = $this->pdo->prepare("SELECT assigned_server_id FROM subnets WHERE subnet = ?");
            $stmt->execute([$firstSubnet]);
            $oldSubnetData = $stmt->fetch();
            
            if ($oldSubnetData && $oldSubnetData['assigned_server_id'] === null) {
                $this->testResults['conflict_resolution'] = 'PASS';
                $this->log("✓ Conflict resolution successful - old subnet properly deallocated");
            } else {
                $this->testResults['conflict_resolution'] = 'FAIL';
                $this->log("✗ Conflict resolution failed - old subnet still assigned");
            }
            
        } catch (Exception $e) {
            $this->testResults['conflict_resolution'] = 'ERROR';
            $this->log("✗ Exception during conflict resolution test: " . $e->getMessage());
        }
    }

    /**
     * Test server type detection
     */
    private function testServerTypeDetection() {
        $this->log("\n--- Testing Server Type Detection ---");
        
        try {
            // Test dedicated server (ID >= 1000000)
            $dedicatedId = $this->config['test_server_ids']['dedicated'];
            $result1 = $this->callAutoAllocationAPI($dedicatedId, 'dedicated');
            
            // Test blade server (ID < 1000000)
            $bladeId = $this->config['test_server_ids']['blade'];
            $result2 = $this->callAutoAllocationAPI($bladeId, 'blade');
            
            if ($result1['success'] && $result2['success']) {
                $this->testResults['server_type_detection'] = 'PASS';
                $this->log("✓ Server type detection working correctly");
            } else {
                $this->testResults['server_type_detection'] = 'FAIL';
                $this->log("✗ Server type detection failed");
            }
            
        } catch (Exception $e) {
            $this->testResults['server_type_detection'] = 'ERROR';
            $this->log("✗ Exception during server type detection test: " . $e->getMessage());
        }
    }

    /**
     * Test location preference
     */
    private function testLocationPreference() {
        $this->log("\n--- Testing Location Preference ---");
        
        try {
            $serverId = $this->config['test_server_ids']['dedicated'];
            $locationId = $this->config['test_location_id'];
            
            // Call with specific location preference
            $result = $this->callAutoAllocationAPI($serverId, 'dedicated', $locationId);
            
            if ($result['success']) {
                $this->testResults['location_preference'] = 'PASS';
                $this->log("✓ Location preference test successful");
            } else {
                $this->testResults['location_preference'] = 'FAIL';
                $this->log("✗ Location preference test failed: " . ($result['error'] ?? 'Unknown error'));
            }
            
        } catch (Exception $e) {
            $this->testResults['location_preference'] = 'ERROR';
            $this->log("✗ Exception during location preference test: " . $e->getMessage());
        }
    }

    /**
     * Test error handling
     */
    private function testErrorHandling() {
        $this->log("\n--- Testing Error Handling ---");
        
        try {
            // Test with invalid server ID
            $result = $this->callAutoAllocationAPI(999999999, 'dedicated');
            
            if (!$result['success'] && isset($result['error'])) {
                $this->testResults['error_handling'] = 'PASS';
                $this->log("✓ Error handling working correctly for invalid server ID");
            } else {
                $this->testResults['error_handling'] = 'FAIL';
                $this->log("✗ Error handling failed - should have returned error for invalid server ID");
            }
            
        } catch (Exception $e) {
            $this->testResults['error_handling'] = 'ERROR';
            $this->log("✗ Exception during error handling test: " . $e->getMessage());
        }
    }

    /**
     * Test database integrity
     */
    private function testDatabaseIntegrity() {
        $this->log("\n--- Testing Database Integrity ---");
        
        try {
            // Check for orphaned allocations
            $stmt = $this->pdo->query("
                SELECT COUNT(*) as count FROM subnets 
                WHERE assigned_server_id IS NOT NULL 
                AND assigned_server_id NOT IN (
                    SELECT id FROM inventory_dedicated_servers 
                    UNION 
                    SELECT id FROM blade_server_inventory
                )
            ");
            $orphanedCount = $stmt->fetch()['count'];
            
            if ($orphanedCount == 0) {
                $this->testResults['database_integrity'] = 'PASS';
                $this->log("✓ Database integrity check passed - no orphaned allocations");
            } else {
                $this->testResults['database_integrity'] = 'FAIL';
                $this->log("✗ Database integrity check failed - found $orphanedCount orphaned allocations");
            }
            
        } catch (Exception $e) {
            $this->testResults['database_integrity'] = 'ERROR';
            $this->log("✗ Exception during database integrity test: " . $e->getMessage());
        }
    }

    /**
     * Test logging functionality
     */
    private function testLoggingFunctionality() {
        $this->log("\n--- Testing Logging Functionality ---");
        
        try {
            $serverId = $this->config['test_server_ids']['dedicated'];
            
            // Count existing log entries
            $stmt = $this->pdo->prepare("SELECT COUNT(*) as count FROM subnet_allocation_log WHERE server_id = ?");
            $stmt->execute([$serverId]);
            $beforeCount = $stmt->fetch()['count'];
            
            // Perform allocation
            $result = $this->callAutoAllocationAPI($serverId, 'dedicated');
            
            // Count log entries after allocation
            $stmt->execute([$serverId]);
            $afterCount = $stmt->fetch()['count'];
            
            if ($afterCount > $beforeCount) {
                $this->testResults['logging_functionality'] = 'PASS';
                $this->log("✓ Logging functionality working correctly");
            } else {
                $this->testResults['logging_functionality'] = 'FAIL';
                $this->log("✗ Logging functionality failed - no new log entries created");
            }
            
        } catch (Exception $e) {
            $this->testResults['logging_functionality'] = 'ERROR';
            $this->log("✗ Exception during logging functionality test: " . $e->getMessage());
        }
    }

    /**
     * Make API call to auto allocation endpoint
     */
    private function callAutoAllocationAPI($serverId, $serverType, $locationId = null) {
        $data = [
            'server_id' => $serverId,
            'server_type' => $serverType,
            'location_id' => $locationId
        ];
        
        $postData = json_encode($data);
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'http://localhost/api_admin_subnets.php?f=auto_allocate_subnet');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($postData)
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            return ['success' => false, 'error' => "HTTP $httpCode"];
        }
        
        return json_decode($response, true) ?: ['success' => false, 'error' => 'Invalid JSON response'];
    }

    /**
     * Cleanup test environment
     */
    private function cleanupTestEnvironment() {
        $this->log("\nCleaning up test environment...");
        
        try {
            // Restore original server states
            foreach ($this->originalServerStates as $serverId => $originalState) {
                $serverType = ($serverId >= 1000000) ? 'dedicated' : 'blade';
                $table = ($serverType === 'dedicated') ? 'inventory_dedicated_servers' : 'blade_server_inventory';
                
                $stmt = $this->pdo->prepare("UPDATE $table SET main_ip = ?, additional_ips = ? WHERE id = ?");
                $stmt->execute([
                    $originalState['main_ip'],
                    $originalState['additional_ips'],
                    $serverId
                ]);
            }
            
            // Clean up test subnets
            foreach ($this->testSubnets as $subnetId) {
                $stmt = $this->pdo->prepare("DELETE FROM subnets WHERE id = ? AND note LIKE '%TEST SUBNET%'");
                $stmt->execute([$subnetId]);
            }
            
            // Clean up test log entries
            $stmt = $this->pdo->prepare("DELETE FROM subnet_allocation_log WHERE server_id IN (" . 
                implode(',', array_keys($this->originalServerStates)) . ")");
            $stmt->execute();
            
            $this->log("Cleanup complete.");
            
        } catch (Exception $e) {
            $this->log("Error during cleanup: " . $e->getMessage());
        }
    }

    /**
     * Report test results
     */
    private function reportResults() {
        $this->log("\n=== TEST RESULTS SUMMARY ===");
        
        $totalTests = 0;
        $passedTests = 0;
        
        foreach ($this->testResults as $testName => $result) {
            if (is_array($result)) {
                foreach ($result as $subTest => $subResult) {
                    $totalTests++;
                    if ($subResult === 'PASS') $passedTests++;
                    $this->log("$testName ($subTest): $subResult");
                }
            } else {
                $totalTests++;
                if ($result === 'PASS') $passedTests++;
                $this->log("$testName: $result");
            }
        }
        
        $this->log("\nOVERALL: $passedTests/$totalTests tests passed");
        
        if ($passedTests === $totalTests) {
            $this->log("🎉 ALL TESTS PASSED! Automatic subnet allocation system is working correctly.");
        } else {
            $this->log("⚠️  SOME TESTS FAILED! Please review the results and fix any issues.");
        }
    }

    /**
     * Log message with timestamp
     */
    private function log($message) {
        if ($this->config['verbose_output']) {
            echo "[" . date('Y-m-d H:i:s') . "] " . $message . "\n";
        }
    }
}

/**
 * Integration Test for Order Processing
 */
class OrderProcessingIntegrationTest {
    private $pdo;

    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    /**
     * Test order processing integration
     */
    public function testOrderProcessingIntegration() {
        echo "\n=== Testing Order Processing Integration ===\n";

        try {
            // Create a test order
            $orderId = $this->createTestOrder();
            echo "Created test order: $orderId\n";

            // Test status update with automatic allocation
            $result = $this->updateOrderStatus($orderId, 'Installing');

            if ($result && isset($result['subnet_allocations'])) {
                echo "✓ Order processing integration successful\n";
                echo "  Subnet allocations: " . count($result['subnet_allocations']) . "\n";

                foreach ($result['subnet_allocations'] as $allocation) {
                    $status = $allocation['result']['success'] ? 'SUCCESS' : 'FAILED';
                    echo "  Server {$allocation['server_id']} ({$allocation['server_type']}): $status\n";
                }
            } else {
                echo "✗ Order processing integration failed\n";
            }

            // Cleanup
            $this->cleanupTestOrder($orderId);

        } catch (Exception $e) {
            echo "Error in order processing integration test: " . $e->getMessage() . "\n";
        }
    }

    private function createTestOrder() {
        // This would create a test order with server assignments
        // Implementation depends on your order structure
        return 1; // Placeholder
    }

    private function updateOrderStatus($orderId, $status) {
        // This would call the order status update API
        // Implementation depends on your API structure
        return ['subnet_allocations' => []]; // Placeholder
    }

    private function cleanupTestOrder($orderId) {
        // Cleanup test order
    }
}

// Run tests if script is executed directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    try {
        echo "Choose test type:\n";
        echo "1. Full allocation system tests\n";
        echo "2. Order processing integration test\n";
        echo "3. Both\n";

        $choice = readline("Enter choice (1-3): ");

        if ($choice == '1' || $choice == '3') {
            $tester = new SubnetAllocationTester($pdo, $TEST_CONFIG);
            $tester->runAllTests();
        }

        if ($choice == '2' || $choice == '3') {
            $integrationTest = new OrderProcessingIntegrationTest($pdo);
            $integrationTest->testOrderProcessingIntegration();
        }

    } catch (Exception $e) {
        echo "FATAL ERROR: " . $e->getMessage() . "\n";
        exit(1);
    }
}
?>
