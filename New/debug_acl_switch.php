<?php
/**
 * Debug script for ACL switch operations
 * This script will test actual switch connectivity and ACL commands
 */

require_once 'mysql.php';
require_once 'pxe_api_integration.php';

// Configuration - Update these with your server details
$TEST_SERVER_ID = 80;
$TEST_SERVER_TYPE = 'blade';

echo "=== ACL Switch Debug Script ===\n\n";

try {
    // Initialize the PXE manager
    $manager = new PXENetworkManager($pdo);
    
    echo "1. Getting Server Information\n";
    echo "-----------------------------\n";
    
    // Use reflection to access private method
    $reflection = new ReflectionClass($manager);
    $getServerInfoMethod = $reflection->getMethod('getServerInfo');
    $getServerInfoMethod->setAccessible(true);
    
    $server = $getServerInfoMethod->invoke($manager, $TEST_SERVER_ID, $TEST_SERVER_TYPE);
    
    if (!$server) {
        throw new Exception("Server not found: $TEST_SERVER_ID");
    }
    
    echo "Server Details:\n";
    echo "- ID: " . $server['id'] . "\n";
    echo "- Label: " . ($server['label'] ?? 'Not set') . "\n";
    echo "- Switch IP: " . ($server['switch_ip'] ?? 'Not configured') . "\n";
    echo "- Port ID: " . ($server['port1'] ?? 'Not configured') . "\n";
    echo "- Port Name: " . ($server['port_name'] ?? 'Not found in inventory_switch_ports') . "\n";
    echo "- Main IP: " . ($server['main_ip'] ?? 'Not set') . "\n";
    echo "- Additional IPs: " . ($server['additional_ips'] ?? 'None') . "\n";
    
    if (empty($server['switch_ip'])) {
        throw new Exception("Server has no switch IP configured");
    }
    
    if (empty($server['port1'])) {
        throw new Exception("Server has no port configured");
    }
    
    echo "\n2. Getting Server Subnets\n";
    echo "--------------------------\n";
    
    $getServerSubnetsMethod = $reflection->getMethod('getServerSubnets');
    $getServerSubnetsMethod->setAccessible(true);
    
    $subnets = $getServerSubnetsMethod->invoke($manager, $TEST_SERVER_ID, $TEST_SERVER_TYPE);
    
    if (empty($subnets)) {
        echo "No subnets found for this server.\n";
        echo "This means ACL operations will be skipped.\n";
        exit(1);
    }
    
    echo "Found subnets:\n";
    foreach ($subnets as $subnet) {
        echo "- $subnet\n";
    }
    
    echo "\n3. Testing Switch Connectivity\n";
    echo "-------------------------------\n";
    
    $switchIp = $server['switch_ip'];
    $switchPassword = $server['root_password'];
    
    echo "Testing SSH connection to $switchIp...\n";
    
    if (!extension_loaded('ssh2')) {
        throw new Exception("SSH2 extension not available");
    }
    
    $connection = @ssh2_connect($switchIp, 22, ['timeout' => 10]);
    if (!$connection) {
        throw new Exception("Could not connect to switch $switchIp");
    }
    
    echo "✓ SSH connection established\n";
    
    if (!@ssh2_auth_password($connection, 'admin', $switchPassword)) {
        ssh2_disconnect($connection);
        throw new Exception("SSH authentication failed");
    }
    
    echo "✓ SSH authentication successful\n";
    
    $shell = ssh2_shell($connection, 'vt102');
    if (!$shell) {
        ssh2_disconnect($connection);
        throw new Exception("Could not open SSH shell");
    }
    
    echo "✓ SSH shell opened\n";
    
    // Test basic command
    echo "\n4. Testing Basic Switch Commands\n";
    echo "---------------------------------\n";
    
    stream_set_blocking($shell, false);
    
    // Send a simple command to test
    $testCommand = "show version";
    echo "Sending test command: $testCommand\n";
    
    fwrite($shell, $testCommand . "\n");
    fflush($shell);
    
    sleep(2);
    
    $output = '';
    $attempts = 0;
    while ($attempts < 10) {
        $data = fread($shell, 4096);
        if ($data !== false && strlen($data) > 0) {
            $output .= $data;
        } else {
            usleep(100000);
        }
        $attempts++;
    }
    
    echo "Response received (" . strlen($output) . " characters):\n";
    echo "---\n";
    echo substr($output, 0, 500) . (strlen($output) > 500 ? "...[truncated]" : "") . "\n";
    echo "---\n";
    
    echo "\n5. Testing ACL Commands\n";
    echo "------------------------\n";
    
    // Create ACL removal commands
    $createACLRemovalMethod = $reflection->getMethod('createACLRemovalCommands');
    $createACLRemovalMethod->setAccessible(true);
    
    $portNumber = $server['port1'];
    $portName = $server['port_name'];
    $serverLabel = $server['label'] ?? null;

    $aclRemovalCommands = $createACLRemovalMethod->invoke($manager, $portNumber, $portName, $subnets, $serverLabel);
    
    echo "ACL Removal Commands to be executed:\n";
    foreach ($aclRemovalCommands as $i => $cmd) {
        echo ($i + 1) . ". $cmd\n";
    }
    
    echo "\nExecuting ACL removal commands...\n";
    
    foreach ($aclRemovalCommands as $index => $command) {
        echo "Executing: $command\n";
        
        fwrite($shell, $command . "\n");
        fflush($shell);
        
        usleep(1000000); // Wait 1 second
        
        $cmdOutput = '';
        $attempts = 0;
        while ($attempts < 5) {
            $data = fread($shell, 4096);
            if ($data !== false && strlen($data) > 0) {
                $cmdOutput .= $data;
            } else {
                usleep(200000);
            }
            $attempts++;
        }
        
        echo "Response: " . (strlen($cmdOutput) > 0 ? substr($cmdOutput, 0, 200) : "[no response]") . "\n";
        echo "---\n";
    }
    
    fclose($shell);
    ssh2_disconnect($connection);
    
    echo "\n=== Debug Complete ===\n";
    echo "Check the logs for detailed information about the ACL operations.\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n";
?>
