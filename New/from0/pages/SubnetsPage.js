import React, { useState, useEffect, useMemo } from 'react';
import {
  Search,
  Plus,
  Filter,
  ArrowUp,
  ArrowDown,
  Eye,
  XCircle,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Network,
  Server,
  Hash,
  Wifi,
  Map,
  Trash2,
  Unlink,
  Link,
  Divide,
  Tag,
  Edit,
  Save,
  X,
  BarChart3,
  Clock
} from 'lucide-react';
import Sidebar from '../components/Sidebar';
import TopMenu from '../components/TopMenu';
import AddSubnetModal from '../components/AddSubnetModal';
import DivideSubnetModal from '../components/DivideSubnetModal';
import AssignSubnetModal from '../components/AssignSubnetModal';
import AllocateSubnetModal from '../components/AllocateSubnetModal';
import AllocateIpModal from '../components/AllocateIpModal';
import AllocationHistoryModal from '../components/AllocationHistoryModal';
import AllocationStatsModal from '../components/AllocationStatsModal';
import Pagination from '../components/Pagination';
import axios from 'axios';
import { toast } from 'react-toastify';
import CompactSubnetTree from '../components/CompactSubnetTree';
import { API_URL } from '../config';
const SubnetsPage = ({ navigateTo, sidebarCollapsed, toggleSidebar }) => {
  // State for subnets data and loading
  const [subnets, setSubnets] = useState([]);
  const [mainSubnets, setMainSubnets] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState('Never');
  const mainSubnetsArray = Array.isArray(mainSubnets) ? mainSubnets : [];
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(20);

  // Calculate total pages
  const totalPages = Math.ceil(mainSubnetsArray.length / itemsPerPage);
  // Filter and sort state
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('All');
  const [selectedCategory, setSelectedCategory] = useState('Root');
  const [selectedLocation, setSelectedLocation] = useState('All');
  const [sortField, setSortField] = useState('lastUpdated');
  const [sortDirection, setSortDirection] = useState('desc');
  const [allSubnets, setAllSubnets] = useState([]);
  // Modal state
  const [selectedSubnet, setSelectedSubnet] = useState(null);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isDivideModalOpen, setIsDivideModalOpen] = useState(false);
  const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);
  const [isAllocateModalOpen, setIsAllocateModalOpen] = useState(false);
  const [isAllocateIpModalOpen, setIsAllocateIpModalOpen] = useState(false);
  const [isAllocationHistoryModalOpen, setIsAllocationHistoryModalOpen] = useState(false);
  const [isAllocationStatsModalOpen, setIsAllocationStatsModalOpen] = useState(false);
  const [selectedIpAddress, setSelectedIpAddress] = useState(null);
  const [selectedServerId, setSelectedServerId] = useState(null);
  const [selectedServerType, setSelectedServerType] = useState(null);

  // State for refreshing IP addresses in the tree
  const [ipRefreshTrigger, setIpRefreshTrigger] = useState(0);

  // State for forcing re-renders to refresh buttons
  const [renderTrigger, setRenderTrigger] = useState(0);

  // State for editing notes
  const [isEditingNote, setIsEditingNote] = useState(false);
  const [editingNote, setEditingNote] = useState('');

  // State for filter dropdowns
  const [uniqueCategories, setUniqueCategories] = useState(['All']);
  const [uniqueLocations, setUniqueLocations] = useState(['All']);
  const [uniqueStatuses, setUniqueStatuses] = useState(['All']);

  // Statistics state
  const [stats, setStats] = useState({
    totalIps: 0,
    usedIps: 0,
    utilizationPercent: 0,
    locationCount: 0,
    networkPerformance: 99.98
  });

  // Fetch subnets from API
  const fetchSubnets = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // First fetch all subnets for the tree (unfiltered)
      await fetchAllSubnetsForTree();

      // Then fetch filtered subnets for the main view
      const response = await axios.post(`${API_URL}/api_admin_subnets.php?f=get_subnets`, {
        token: localStorage.getItem('admin_token'),
        status: selectedStatus !== 'All' ? selectedStatus : '',
        category: selectedCategory !== 'All' ? selectedCategory : '',
        location: selectedLocation !== 'All' ? selectedLocation : '',
        search: searchQuery || '',
        sortField: sortField,
        sortDirection: sortDirection
      });

      if (response.data && Array.isArray(response.data)) {
        setSubnets(response.data);

        // Ensure all categories are always available
        const categories = [
          'All',
          'Root',
          'Customer',
          'Internal',
          'Management'
        ];
        setUniqueCategories(categories);

        // Filter to only main subnets (those without a parentId or with parentId = null/0)
        const mainSubnetsOnly = response.data.filter(subnet =>
          !subnet.parentId || subnet.parentId === 0 || subnet.parentId === null
        );
        setMainSubnets(mainSubnetsOnly);

        // Update last updated timestamp
        setLastUpdated(new Date().toLocaleTimeString('en-US', { hour12: false, hour: '2-digit', minute: '2-digit' }));
      }
    } catch (err) {
      console.error('Error fetching subnets:', err);
      setError('Failed to fetch subnets');
      toast.error('Failed to fetch subnets');
    } finally {
      setIsLoading(false);
    }
  };


  // Initial data fetch
  useEffect(() => {
    fetchSubnets();
    fetchAllSubnetsForTree();
  }, []);

  // Refetch when filters or sort change
  useEffect(() => {
    fetchSubnets();
  }, [selectedStatus, selectedCategory, selectedLocation, sortField, sortDirection]);

  useEffect(() => {
    if (currentPage > totalPages) {
      setCurrentPage(Math.max(1, totalPages));
    }
  }, [mainSubnetsArray, totalPages]);

  // Manual refresh
  const handleRefreshData = () => {
    fetchSubnets();
  };

  // Search handling with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      fetchSubnets();
    }, 500);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Handle search input
  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Filter handlers
  const handleStatusFilter = (status) => {
    setSelectedStatus(status);
    setCurrentPage(1); // Reset to first page when filtering
  };

  const handleCategoryFilter = (category) => {
    setSelectedCategory(category);
    setCurrentPage(1); // Reset to first page when filtering
  };

  const handleLocationFilter = (location) => {
    setSelectedLocation(location);
    setCurrentPage(1); // Reset to first page when filtering
  };

  // Sort handlers
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
    // Reset to first page when sorting changes
    setCurrentPage(1);
  };

  const fetchAllSubnetsForTree = async () => {
    try {
      console.log('Fetching all subnets for tree...');

      const response = await axios.post(`${API_URL}/api_admin_subnets.php?f=get_subnets`, {
        token: localStorage.getItem('admin_token'),
        // No filters to ensure we get ALL subnets
      });

      if (response.data && Array.isArray(response.data)) {
        console.log(`Fetched ${response.data.length} subnets for tree`);

        // Ensure all subnets have the required fields for the tree
        const processedSubnets = response.data.map(subnet => ({
          ...subnet,
          // Ensure these fields exist for the tree component
          cidr: subnet.subnet || subnet.cidr || 'Unknown',
          category: subnet.category || 'Unknown',
          id: subnet.id // Keep the ID as is
        }));

        // Process subnets to ensure parent subnets of unavailable/allocated/assigned subnets are also marked as unavailable
        const updatedSubnets = markParentSubnetsAsUnavailable(processedSubnets);

        // Run the function a second time to catch any cascading effects
        // (where marking a parent as unavailable should make its parents unavailable too)
        const finalSubnets = markParentSubnetsAsUnavailable(updatedSubnets);

        console.log('Processed subnets for tree:', finalSubnets);
        setAllSubnets(finalSubnets);
      } else {
        console.warn('Invalid response format for subnets:', response.data);
      }
    } catch (err) {
      console.error('Error fetching all subnets for tree:', err);
      // Don't show an error toast here to avoid duplicate notifications
    }
  };

  // Helper function to check if one CIDR is a parent of another
  const isCidrParent = (parentCidr, childCidr) => {
    if (!parentCidr || !childCidr || !parentCidr.includes('/') || !childCidr.includes('/')) {
      return false;
    }

    try {
      // Extract network addresses and masks
      const [parentNetwork, parentMask] = parentCidr.split('/');
      const [childNetwork, childMask] = childCidr.split('/');

      // Parse masks as integers
      const parentMaskInt = parseInt(parentMask, 10);
      const childMaskInt = parseInt(childMask, 10);

      // Parent must have a smaller mask (larger network)
      if (parentMaskInt >= childMaskInt) {
        return false;
      }

      // Convert IP addresses to long integers for more accurate comparison
      const ipToLong = (ip) => {
        const octets = ip.split('.');
        return ((parseInt(octets[0], 10) << 24) >>> 0) +
               ((parseInt(octets[1], 10) << 16) >>> 0) +
               ((parseInt(octets[2], 10) << 8) >>> 0) +
               parseInt(octets[3], 10);
      };

      const parentLong = ipToLong(parentNetwork);
      const childLong = ipToLong(childNetwork);

      // Calculate network addresses with their respective masks
      const parentNetworkAddr = parentLong & ((0xffffffff << (32 - parentMaskInt)) >>> 0);
      const childNetworkAddr = childLong & ((0xffffffff << (32 - childMaskInt)) >>> 0);

      // Calculate if the child network is within the parent network
      const childIsInParent = (childNetworkAddr >>> (32 - parentMaskInt)) === (parentNetworkAddr >>> (32 - parentMaskInt));

      console.log(`Checking if ${parentCidr} is parent of ${childCidr}: ${childIsInParent}`);

      return childIsInParent;
    } catch (error) {
      console.error('Error comparing CIDRs:', error, { parentCidr, childCidr });
      return false;
    }
  };

  // Helper function to mark parent subnets as unavailable
  const markParentSubnetsAsUnavailable = (subnetsData) => {
    // Create a copy of the subnets array to avoid mutating the original
    const processedSubnets = [...subnetsData];

    // First, identify all unavailable subnets
    // For IP allocation, we only want to mark parent subnets as unavailable, not the subnet itself
    // So we only consider subnets that are already marked as Unavailable, Allocated, or Assigned
    const unavailableSubnets = processedSubnets.filter(subnet =>
      subnet.status === 'Unavailable' ||
      subnet.status === 'Allocated' ||
      subnet.status === 'Assigned'
    );

    console.log(`Found ${unavailableSubnets.length} unavailable/allocated/assigned subnets`);

    // For each unavailable subnet, mark all its parents as unavailable
    unavailableSubnets.forEach(unavailableSubnet => {
      if (!unavailableSubnet.cidr && !unavailableSubnet.subnet) {
        console.warn('Subnet missing CIDR information:', unavailableSubnet);
        return;
      }

      const unavailableCidr = unavailableSubnet.cidr || unavailableSubnet.subnet;
      console.log(`Processing unavailable subnet: ${unavailableCidr} (${unavailableSubnet.id})`);

      // Find all parent subnets based on CIDR notation
      processedSubnets.forEach((potentialParent, index) => {
        if (potentialParent.id === unavailableSubnet.id) return; // Skip self

        const parentCidr = potentialParent.cidr || potentialParent.subnet;
        if (!parentCidr) return;

        // Check if this is a parent subnet based on CIDR
        if (isCidrParent(parentCidr, unavailableCidr)) {
          console.log(`Marking parent subnet ${potentialParent.id} (${parentCidr}) as unavailable because it contains ${unavailableCidr}`);
          processedSubnets[index] = { ...processedSubnets[index], status: 'Unavailable' };
        } else {
          console.log(`Subnet ${potentialParent.id} (${parentCidr}) is NOT a parent of ${unavailableCidr}`);
        }
      });

      // Also use the existing findParentSubnets function as a backup
      const parentSubnets = findParentSubnets(unavailableSubnet, processedSubnets);

      if (parentSubnets.length > 0) {
        console.log(`Found ${parentSubnets.length} parent subnets for unavailable subnet ${unavailableSubnet.id} using relationship map`);

        // Verify each parent subnet using CIDR before marking as unavailable
        parentSubnets.forEach(parentSubnet => {
          const parentCidr = parentSubnet.cidr || parentSubnet.subnet;
          if (!parentCidr) return;

          // Double-check with CIDR logic
          if (isCidrParent(parentCidr, unavailableCidr)) {
            const parentIndex = processedSubnets.findIndex(s => s.id === parentSubnet.id);
            if (parentIndex !== -1) {
              console.log(`Marking parent subnet ${parentSubnet.id} (${parentCidr}) as unavailable (verified with CIDR)`);
              processedSubnets[parentIndex] = { ...processedSubnets[parentIndex], status: 'Unavailable' };
            }
          } else {
            console.log(`Relationship map indicated ${parentSubnet.id} (${parentCidr}) as parent of ${unavailableCidr}, but CIDR check failed`);
          }
        });
      }
    });

    return processedSubnets;
  };

  // Get sort icon based on current sort field/direction
  const getSortIcon = (field) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ?
      <ArrowUp className="w-4 h-4 inline ml-1" /> :
      <ArrowDown className="w-4 h-4 inline ml-1" />;
  };

  // Pagination handlers
  const nextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const prevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const goToPage = (page) => {
    const totalPages = Math.ceil(mainSubnetsArray.length / itemsPerPage);
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  // Pagination handlers for Pagination component
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    const totalPages = Math.ceil(mainSubnetsArray.length / itemsPerPage);
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  // Modal handlers
  const handleSubnetClick = (subnet) => {
    setSelectedSubnet(subnet);
    setIsEditingNote(false);
    setEditingNote('');
  };

  const closeSubnetDetails = () => {
    setSelectedSubnet(null);
    setIsEditingNote(false);
    setEditingNote('');
  };

  const openAddSubnetModal = () => {
    setIsAddModalOpen(true);
  };

  const closeAddSubnetModal = () => {
    setIsAddModalOpen(false);
  };

  const openDivideSubnetModal = (subnet) => {
    setSelectedSubnet(subnet);
    setIsDivideModalOpen(true);
  };

  const closeDivideSubnetModal = () => {
    setIsDivideModalOpen(false);
  };

  const openAssignSubnetModal = (subnet) => {
    setSelectedSubnet(subnet);
    setIsAssignModalOpen(true);
  };

  const closeAssignSubnetModal = () => {
    setIsAssignModalOpen(false);
  };

  const openAllocateSubnetModal = (subnet) => {
    setSelectedSubnet(subnet);
    setIsAllocateModalOpen(true);
  };

  const closeAllocateSubnetModal = () => {
    setIsAllocateModalOpen(false);
  };

  // CRUD operations
  const handleAddSubnet = async (subnetData) => {
    try {
      const response = await axios.post(`${API_URL}/api_admin_subnets.php?f=add_subnet`, {
        ...subnetData,
        token: localStorage.getItem('admin_token')
      }, {
        validateStatus: function (status) {
          return status >= 200 && status < 300;
        }
      });

      console.log('Add Subnet Full Response:', response);

      if (response.data.success) {
        toast.success('Subnet added successfully');
        fetchSubnets();
        closeAddSubnetModal();
      } else {
        throw new Error(response.data.error || 'Failed to add subnet');
      }
    } catch (err) {
      console.error('Add Subnet Detailed Error:', {
        message: err.message,
        response: err.response?.data,
        status: err.response?.status,
        headers: err.response?.headers
      });

      const errorMessage = err.response?.data?.error ||
                           err.response?.data?.message ||
                           'Error adding subnet';

      toast.error(errorMessage);
    }
  };

  // Global flag to track if user has already confirmed deletion
  const [hasConfirmedDeletion, setHasConfirmedDeletion] = useState(false);

  const handleDeleteSubnet = async (subnetId, force = false, skipConfirmation = false) => {
    // Find the subnet to check its status
    const subnetToDelete = subnets.find(s => s.id === subnetId) || allSubnets.find(s => s.id === subnetId);
    
    // Prevent deletion of allocated, assigned, or unavailable subnets unless forced
    if (!force && subnetToDelete) {
      if (subnetToDelete.status === 'Allocated') {
        toast.error('Cannot delete subnet: This subnet is allocated/reserved. Please deallocate it first.');
        return;
      }
      if (subnetToDelete.status === 'Assigned') {
        toast.error('Cannot delete subnet: This subnet is assigned to a server. Please unassign it first.');
        return;
      }
      if (subnetToDelete.status === 'Unavailable') {
        toast.error('Cannot delete subnet: This subnet has allocated children or is part of an allocated hierarchy.');
        return;
      }
    }

    // Skip confirmation if explicitly requested or if user has already confirmed a deletion in this batch
    if (!skipConfirmation && !hasConfirmedDeletion && !force) {
      if (!window.confirm('Are you sure you want to delete this subnet?')) {
        return;
      }
      // Set the flag to true so we don't ask again for subsequent deletions
      setHasConfirmedDeletion(true);

      // Reset the flag after 5 seconds (for the next batch of deletions)
      setTimeout(() => {
        setHasConfirmedDeletion(false);
      }, 5000);
    }

    try {
      const response = await axios.post(`${API_URL}/api_admin_subnets.php?f=delete_subnet`, {
        subnet_id: subnetId,
        force: force,
        token: localStorage.getItem('admin_token')
      });

      if (response.data.success) {
        // Don't show toast for every deletion in a batch
        if (!skipConfirmation) {
          toast.success('Subnet deleted successfully');
        }
        fetchSubnets();
        if (selectedSubnet && selectedSubnet.id === subnetId) {
          closeSubnetDetails();
        }
      }
    } catch (err) {
      console.error('Error deleting subnet:', err);

      // Check if it's because the subnet is assigned
      if (err.response?.data?.error?.includes('assigned to a server')) {
        if (hasConfirmedDeletion || window.confirm('This subnet is assigned to a server. Force delete?')) {
          // If user has already confirmed deletion in this batch, don't ask again
          handleDeleteSubnet(subnetId, true, true);
        }
      } else if (err.response?.data?.error?.includes('has child subnets')) {
        if (hasConfirmedDeletion || window.confirm('This subnet has child subnets. Force delete all?')) {
          // If user has already confirmed deletion in this batch, don't ask again
          handleDeleteSubnet(subnetId, true, true);
        }
      } else {
        toast.error('Error deleting subnet: ' + (err.response?.data?.error || err.message));
      }
    }
  };

  // Helper function to check if a subnet has children
  const checkSubnetHasChildren = (subnet) => {
    if (!subnet || !allSubnets || allSubnets.length === 0) return false;

    // Check if this subnet is a parent of any other subnet
    return allSubnets.some(potentialChild => {
      if (potentialChild.id === subnet.id) return false; // Skip self

      // Check if the subnet is a parent based on CIDR
      return isCidrParent(subnet.cidr, potentialChild.cidr);
    });
  };

  // Get number of direct children for a subnet
  const getDirectChildCount = (subnet) => {
    if (!subnet || !allSubnets || allSubnets.length === 0) return 0;
    return allSubnets.filter(potentialChild => {
      if (potentialChild.id === subnet.id) return false;
      return isCidrParent(subnet.cidr, potentialChild.cidr);
    }).length;
  };

  // Helper function to check if a subnet has any children with allocated subnets or IPs
  const checkSubnetHasAllocatedChildren = (subnet) => {
    if (!subnet || !allSubnets || allSubnets.length === 0) return false;

    // Get all direct children of this subnet
    const directChildren = allSubnets.filter(potentialChild => {
      if (potentialChild.id === subnet.id) return false; // Skip self
      return isCidrParent(subnet.cidr, potentialChild.cidr);
    });

    // Check if any direct child has allocated IPs or is allocated/assigned
    const hasAllocatedDirectChildren = directChildren.some(child =>
      child.status === 'Allocated' ||
      child.status === 'Assigned' ||
      child.status === 'Unavailable'
    );

    if (hasAllocatedDirectChildren) {
      return true;
    }

    // Recursively check all children
    return directChildren.some(child => checkSubnetHasAllocatedChildren(child));
  };

  // Helper function to get all children of a subnet
  const getAllSubnetChildren = (subnet) => {
    if (!subnet || !allSubnets || allSubnets.length === 0) return [];

    // Get all direct children of this subnet
    const directChildren = allSubnets.filter(potentialChild => {
      if (potentialChild.id === subnet.id) return false; // Skip self
      return isCidrParent(subnet.cidr, potentialChild.cidr);
    });

    // Get all children recursively
    let allChildren = [...directChildren];

    directChildren.forEach(child => {
      const childrenOfChild = getAllSubnetChildren(child);
      allChildren = [...allChildren, ...childrenOfChild];
    });

    return allChildren;
  };

  // Helper function to delete all children of a subnet
  const deleteAllSubnetChildren = async (subnet) => {
    const children = getAllSubnetChildren(subnet);

    if (children.length === 0) {
      return { success: true, message: "No children to delete" };
    }

    console.log(`Deleting ${children.length} children of subnet ${subnet.id}`);

    // Delete children in reverse order (deepest first)
    const reversedChildren = [...children].reverse();

    // Ask for confirmation only once for the first child
    let isFirstChild = true;

    for (const child of reversedChildren) {
      try {
        console.log(`Deleting child subnet ${child.id}`);

        if (isFirstChild) {
          // For the first child, let the confirmation dialog show
          await handleDeleteSubnet(child.id, true, false);
          isFirstChild = false;
        } else {
          // For subsequent children, skip the confirmation dialog
          await handleDeleteSubnet(child.id, true, true);
        }
      } catch (error) {
        console.error(`Error deleting child subnet ${child.id}:`, error);
        return { success: false, message: `Error deleting child subnet ${child.id}: ${error.message}` };
      }
    }

    return { success: true, message: `Successfully deleted ${children.length} child subnets` };
  };

  // Helper function to check if a subnet has any allocated IPs
  const checkSubnetHasAllocatedIps = async (subnetId) => {
    try {
      // Extract numeric ID if needed
      const numericId = typeof subnetId === 'string' && subnetId.startsWith('SUB-')
        ? parseInt(subnetId.replace('SUB-', ''))
        : subnetId;

      // Fetch the subnet's IPs
      const response = await axios.post(
        `${API_URL}/api_admin_subnets.php?f=get_subnet_ips`,
        {
          subnet_id: numericId,
          token: localStorage.getItem('admin_token')
        }
      );

      if (response.data && response.data.success && response.data.ips) {
        // Check if any IPs are allocated
        const allocatedIps = response.data.ips.filter(ip => ip.is_used === '1' || ip.is_used === 1);
        return allocatedIps.length > 0;
      }

      return false;
    } catch (error) {
      console.error('Error checking if subnet has allocated IPs:', error);
      return false; // Assume no allocated IPs in case of error
    }
  };

  // Helper function to delete all generated IPs for a subnet
  const deleteGeneratedIps = async (subnetId) => {
    try {
      // Extract numeric ID if needed
      const numericId = typeof subnetId === 'string' && subnetId.startsWith('SUB-')
        ? parseInt(subnetId.replace('SUB-', ''))
        : subnetId;

      console.log(`Deleting generated IPs for subnet ${subnetId}`);

      const response = await axios.post(
        `${API_URL}/api_admin_subnets.php?f=delete_subnet_ips`,
        {
          subnet_id: numericId,
          token: localStorage.getItem('admin_token')
        }
      );

      if (response.data && response.data.success) {
        console.log(`Successfully deleted generated IPs for subnet ${subnetId}`);
        return true;
      } else {
        console.error('Failed to delete generated IPs:', response.data);
        return false;
      }
    } catch (error) {
      console.error('Error deleting generated IPs:', error);
      return false;
    }
  };

  const handleAssignSubnet = async (data) => {
    try {
      console.log('Assign Subnet Request Data:', data);

      // Check if the subnet has any allocated IPs before assigning
      const subnetId = data.subnet_id;
      const hasAllocatedIps = await checkSubnetHasAllocatedIps(subnetId);

      // If the subnet has no allocated IPs, delete any generated IPs
      if (!hasAllocatedIps) {
        console.log(`Subnet ${subnetId} has no allocated IPs, deleting generated IPs before assignment`);
        await deleteGeneratedIps(subnetId);
      } else {
        console.log(`Subnet ${subnetId} has allocated IPs, keeping them during assignment`);
      }

      const response = await axios.post(
        `${API_URL}/api_admin_subnets.php?f=assign_subnet_to_server`,
        {
          ...data,
          token: localStorage.getItem('admin_token')
        },
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Full Assign Subnet Response:', response);

      if (response.data.success) {
        // Show different success message based on whether IPs were deleted
        if (!hasAllocatedIps) {
          toast.success('Subnet assigned successfully and generated IPs were cleared');
        } else {
          toast.success('Subnet assigned successfully');
        }

        // Get the formatted subnet ID
        const formattedSubnetId = typeof data.subnet_id === 'string' && data.subnet_id.startsWith('SUB-')
          ? data.subnet_id
          : `SUB-${String(data.subnet_id).padStart(4, '0')}`;

        // Immediately update the local state to reflect the change
        // This ensures the UI updates immediately without waiting for the fetch
        setSubnets(prevSubnets =>
          prevSubnets.map(s => {
            if (s.id === formattedSubnetId) {
              console.log(`Immediately updating subnet ${s.id} status to Assigned`);
              return {
                ...s,
                status: 'Assigned',
                assigned_server_id: data.server_id,
                server_type: data.server_type
              };
            }
            return s;
          })
        );

        // Also update allSubnets state for the tree view
        setAllSubnets(prevSubnets =>
          prevSubnets.map(s => {
            if (s.id === formattedSubnetId) {
              console.log(`Immediately updating allSubnets entry for ${s.id} to Assigned`);
              return {
                ...s,
                status: 'Assigned',
                assigned_server_id: data.server_id,
                server_type: data.server_type
              };
            }
            return s;
          })
        );

        // If we have a selected subnet that was just assigned, update it too
        if (selectedSubnet && selectedSubnet.id === formattedSubnetId) {
          console.log(`Updating selected subnet ${selectedSubnet.id} status to Assigned`);
          setSelectedSubnet(prev => ({
            ...prev,
            status: 'Assigned',
            assigned_server_id: data.server_id,
            server_type: data.server_type
          }));
        }

        // Fetch updated subnet data from the server (this will happen in the background)
        fetchSubnets();

        // Trigger a refresh of the subnet tree component's IP addresses
        // This is critical for updating the UI to show that IPs have been deleted
        console.log(`Triggering IP refresh after subnet assignment (current value: ${ipRefreshTrigger})`);
        setIpRefreshTrigger(prev => {
          const newValue = prev + 1;
          console.log(`Incrementing IP refresh trigger from ${prev} to ${newValue} after subnet assignment`);
          return newValue;
        });

        // Also refresh the subnet tree to ensure all changes are reflected
        console.log('Refreshing subnet tree after subnet assignment');
        fetchAllSubnetsForTree();

        // Force a re-render of the component to ensure buttons are updated
        setRenderTrigger(prev => prev + 1);

        // Close the modal
        closeAssignSubnetModal();
      } else {
        console.error('Assign Subnet Error:', response.data);
        toast.error(response.data.error || 'Failed to assign subnet');
      }
    } catch (err) {
      console.error('Detailed Error Assigning Subnet:', {
        message: err.message,
        response: err.response?.data,
        status: err.response?.status,
        headers: err.response?.headers
      });

      // More specific error handling
      if (err.response && err.response.data && err.response.data.error) {
        toast.error(err.response.data.error);
      } else {
        toast.error('Error assigning subnet: ' + (err.response?.data?.error || err.message));
      }
    }
  };

  const handleUnassignSubnet = async (subnetId) => {
    if (!window.confirm('Are you sure you want to unassign this subnet?')) {
      return Promise.reject('User cancelled unassign operation');
    }

    try {
      const response = await axios.post(`${API_URL}/api_admin_subnets.php?f=unassign_subnet`, {
        subnet_id: subnetId,
        token: localStorage.getItem('admin_token') // Include the token
      });

      if (response.data.success) {
        toast.success('Subnet unassigned successfully');

        // Get the formatted subnet ID
        const formattedSubnetId = typeof subnetId === 'string' && subnetId.startsWith('SUB-')
          ? subnetId
          : `SUB-${String(subnetId).padStart(4, '0')}`;

        // Immediately update the local state to reflect the change
        setSubnets(prevSubnets =>
          prevSubnets.map(s => {
            if (s.id === formattedSubnetId) {
              console.log(`Immediately updating subnet ${s.id} status to Available after unassign`);
              return {
                ...s,
                status: 'Available',
                assigned_server_id: null,
                server_type: null
              };
            }
            return s;
          })
        );

        // Also update allSubnets state for the tree view
        setAllSubnets(prevSubnets =>
          prevSubnets.map(s => {
            if (s.id === formattedSubnetId) {
              console.log(`Immediately updating allSubnets entry for ${s.id} to Available after unassign`);
              return {
                ...s,
                status: 'Available',
                assigned_server_id: null,
                server_type: null
              };
            }
            return s;
          })
        );

        // If we have a selected subnet that was just unassigned, update it too
        if (selectedSubnet && selectedSubnet.id === formattedSubnetId) {
          console.log(`Updating selected subnet ${selectedSubnet.id} status to Available after unassign`);
          setSelectedSubnet(prev => ({
            ...prev,
            status: 'Available',
            assigned_server_id: null,
            server_type: null
          }));
        }

        // Fetch subnets in the background to update the list
        fetchSubnets();

        // Force a re-render of the component to ensure buttons are updated
        setRenderTrigger(prev => prev + 1);

        return Promise.resolve(response.data);
      } else {
        const error = new Error(response.data.error || 'Failed to unassign subnet');
        toast.error(error.message);
        return Promise.reject(error);
      }
    } catch (err) {
      console.error('Error unassigning subnet:', err);
      toast.error('Error unassigning subnet: ' + (err.response?.data?.error || err.message));
      return Promise.reject(err);
    }
  };

  const handleAllocateSubnet = async (data) => {
    try {
      console.log('Allocate Subnet Request Data:', data);

      const response = await axios.post(
        `${API_URL}/api_admin_subnets.php?f=allocate_subnet`,
        {
          ...data,
          token: localStorage.getItem('admin_token')
        },
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Full Allocate Subnet Response:', response);

      if (response.data.success) {
        toast.success('Subnet allocated successfully');

        // Update parent subnets to be marked as unavailable
        if (response.data.parent_subnets && response.data.parent_subnets.length > 0) {
          console.log(`Marking ${response.data.parent_subnets.length} parent subnets as unavailable`);

          // Update the allSubnets state to mark the allocated subnet and its parent subnets as unavailable
          let updatedSubnets = allSubnets.map(s => {
            // Check if this is the subnet we just allocated
            if (typeof data.subnet_id === 'string' && data.subnet_id.startsWith('SUB-')) {
              if (s.id === data.subnet_id) {
                console.log(`Marking allocated subnet ${s.id} as unavailable`);
                return { ...s, status: 'Unavailable' };
              }
            } else {
              const formattedId = `SUB-${String(data.subnet_id).padStart(4, '0')}`;
              if (s.id === formattedId) {
                console.log(`Marking allocated subnet ${s.id} as unavailable`);
                return { ...s, status: 'Unavailable' };
              }
            }

            // Check if this subnet is a parent of the allocated subnet
            const isParent = response.data.parent_subnets.some(
              parent => parent.formatted_id === s.id || parent.id === s.id
            );

            if (isParent) {
              console.log(`Marking parent subnet ${s.id} as unavailable`);
              return { ...s, status: 'Unavailable' };
            }
            return s;
          });

          // Apply our enhanced parent subnet detection to catch any missed parents
          const processedSubnets = markParentSubnetsAsUnavailable(updatedSubnets);

          // Run the function a second time to catch any cascading effects
          const finalSubnets = markParentSubnetsAsUnavailable(processedSubnets);

          setAllSubnets(finalSubnets);
        } else {
          // If API doesn't return parent subnets, use our helper function
          console.log('API did not return parent subnets, using helper function');

          // Find the allocated subnet in allSubnets
          const allocatedSubnet = allSubnets.find(s => {
            if (typeof data.subnet_id === 'string' && data.subnet_id.startsWith('SUB-')) {
              return s.id === data.subnet_id;
            } else {
              return s.id === `SUB-${String(data.subnet_id).padStart(4, '0')}`;
            }
          });

          if (allocatedSubnet) {
            // Create a copy of allSubnets with the allocated subnet marked as unavailable
            const updatedSubnets = allSubnets.map(s => {
              if (s.id === allocatedSubnet.id) {
                return { ...s, status: 'Unavailable' };
              }
              return s;
            });

            // Use our helper function to mark parent subnets as unavailable
            const processedSubnets = markParentSubnetsAsUnavailable(updatedSubnets);

            // Run the function a second time to catch any cascading effects
            const finalSubnets = markParentSubnetsAsUnavailable(processedSubnets);
            setAllSubnets(finalSubnets);
          }
        }

        fetchSubnets();
        // After updating the state, trigger a refresh of the tree to ensure all parent subnets are properly marked
        fetchAllSubnetsForTree();
        closeAllocateSubnetModal();
      } else {
        console.error('Allocate Subnet Error:', response.data);
        toast.error(response.data.error || 'Failed to allocate subnet');
      }
    } catch (err) {
      console.error('Detailed Error Allocating Subnet:', {
        message: err.message,
        response: err.response?.data,
        status: err.response?.status,
        headers: err.response?.headers
      });

      // More specific error handling
      if (err.response && err.response.data && err.response.data.error) {
        toast.error(err.response.data.error);
      } else {
        toast.error('Error allocating subnet: ' + (err.response?.data?.error || err.message));
      }
    }
  };

  const handleDeallocateSubnet = async (subnetId) => {
    if (!window.confirm('Are you sure you want to deallocate this subnet?')) {
      return Promise.reject('User cancelled deallocate operation');
    }

    try {
      const response = await axios.post(`${API_URL}/api_admin_subnets.php?f=deallocate_subnet`, {
        subnet_id: subnetId,
        token: localStorage.getItem('admin_token')
      });

      if (response.data.success) {
        toast.success('Subnet deallocated successfully');
        
        // Get the formatted subnet ID
        const formattedSubnetId = typeof subnetId === 'string' && subnetId.startsWith('SUB-')
          ? subnetId
          : `SUB-${String(subnetId).padStart(4, '0')}`;

        // Immediately update the local state to reflect the change
        setSubnets(prevSubnets =>
          prevSubnets.map(s => {
            if (s.id === formattedSubnetId) {
              console.log(`Immediately updating subnet ${s.id} status to Available after deallocate`);
              return {
                ...s,
                status: 'Available',
                is_allocated: 0,
                manual_alocation: null
              };
            }
            return s;
          })
        );

        // Also update allSubnets state for the tree view
        setAllSubnets(prevSubnets =>
          prevSubnets.map(s => {
            if (s.id === formattedSubnetId) {
              console.log(`Immediately updating allSubnets entry for ${s.id} to Available after deallocate`);
              return {
                ...s,
                status: 'Available',
                is_allocated: 0,
                manual_alocation: null
              };
            }
            return s;
          })
        );

        // If we have a selected subnet that was just deallocated, update it too
        if (selectedSubnet && selectedSubnet.id === formattedSubnetId) {
          console.log(`Updating selected subnet ${selectedSubnet.id} status to Available after deallocate`);
          setSelectedSubnet(prev => ({
            ...prev,
            status: 'Available',
            is_allocated: 0,
            manual_alocation: null
          }));
        }

        // Fetch subnets in the background to update the list
        fetchSubnets();
        // After updating the state, trigger a refresh of the tree to ensure all parent subnets are properly marked
        fetchAllSubnetsForTree();
        return Promise.resolve(response.data);
      } else {
        const error = new Error(response.data.error || 'Failed to deallocate subnet');
        toast.error(error.message);
        return Promise.reject(error);
      }
    } catch (err) {
      console.error('Error deallocating subnet:', err);
      toast.error('Error deallocating subnet: ' + (err.response?.data?.error || err.message));
      return Promise.reject(err);
    }
  };

  // Function to find all parent subnets of a given subnet
  const findParentSubnets = (subnet, allSubnets) => {
    if (!subnet || !allSubnets || allSubnets.length === 0) {
      return [];
    }

    // Helper function to extract the numeric ID from the subnet ID if needed
    // (This is used in other parts of the code, keeping for consistency)

    // Build a map of child-to-parent relationships
    const childToParentMap = {};

    // Process all subnets to build the relationship map
    allSubnets.forEach(parentSubnet => {
      const parentId = parentSubnet.id;

      // Check if this subnet has any relationships in the data
      if (parentSubnet.children && Array.isArray(parentSubnet.children)) {
        parentSubnet.children.forEach(childId => {
          childToParentMap[childId] = parentId;
        });
      }
    });

    // If we don't have relationship data, try to infer from CIDR
    if (Object.keys(childToParentMap).length === 0) {
      // Sort subnets by CIDR size (largest first)
      const sortedSubnets = [...allSubnets].sort((a, b) => {
        const sizeA = a.cidr ? parseInt(a.cidr.split('/')[1]) : 0;
        const sizeB = b.cidr ? parseInt(b.cidr.split('/')[1]) : 0;
        return sizeA - sizeB; // Smaller prefix = larger subnet
      });

      // For each subnet, find potential parents
      sortedSubnets.forEach(potentialChild => {
        if (!potentialChild.cidr) return;

        const childCidr = potentialChild.cidr;
        const childIp = childCidr.split('/')[0];
        const childSize = parseInt(childCidr.split('/')[1]);

        sortedSubnets.forEach(potentialParent => {
          if (potentialParent.id === potentialChild.id || !potentialParent.cidr) return;

          const parentCidr = potentialParent.cidr;
          const parentIp = parentCidr.split('/')[0];
          const parentSize = parseInt(parentCidr.split('/')[1]);

          // A parent must have a smaller prefix (larger network)
          if (parentSize < childSize) {
            // Check if child IP is within parent network (simplified check)
            const childOctets = childIp.split('.');
            const parentOctets = parentIp.split('.');

            // Simple check: first N octets match where N depends on parent size
            let isWithin = true;
            const octetsToCheck = Math.floor(parentSize / 8);

            for (let i = 0; i < octetsToCheck; i++) {
              if (childOctets[i] !== parentOctets[i]) {
                isWithin = false;
                break;
              }
            }

            if (isWithin) {
              childToParentMap[potentialChild.id] = potentialParent.id;
            }
          }
        });
      });
    }

    console.log('Child to parent map:', childToParentMap);

    // Now, recursively find all parents
    const parents = [];
    let currentId = subnet.id;

    while (currentId) {
      const parentId = childToParentMap[currentId];
      if (parentId) {
        const parentSubnet = allSubnets.find(s => s.id === parentId);
        if (parentSubnet) {
          parents.push(parentSubnet);
          currentId = parentId;
        } else {
          break;
        }
      } else {
        break;
      }
    }

    return parents;
  };

  // Function to generate IPs for a subnet
  const handleGenerateIps = async (subnet, force = false) => {
    // Return a promise that resolves when the operation is complete
    return new Promise(async (resolve, reject) => {
      try {
        // First, check if the subnet has any children with allocated subnets or IPs
        if (!force && checkSubnetHasChildren(subnet)) {
          // Check if any children have allocated subnets or IPs
          if (checkSubnetHasAllocatedChildren(subnet)) {
            const errorMsg = "Cannot generate IPs: This subnet has children with allocated subnets or IPs";
            console.error(errorMsg);
            toast.error(errorMsg);
            reject(new Error(errorMsg));
            return;
          }

          // If the subnet has children but none are allocated, ask for confirmation to delete them
          const confirmed = window.confirm(
            `This subnet has child subnets. Generating IPs will delete all child subnets. Do you want to continue?`
          );

          if (!confirmed) {
            toast.info('IP generation cancelled');
            resolve(); // Resolve the promise even if cancelled
            return;
          }

          // Delete all children before generating IPs
          const deleteResult = await deleteAllSubnetChildren(subnet);
          if (!deleteResult.success) {
            toast.error(`Error deleting child subnets: ${deleteResult.message}`);
            reject(new Error(deleteResult.message));
            return;
          }

          toast.success(deleteResult.message);
        }

        // Show a pending toast to indicate the request is being processed
        const pendingToast = toast.info('Generating IP addresses...', { autoClose: false });

        // Extract subnet ID from SUB-xxxx format if needed
        const subnetId = typeof subnet.id === 'string' && subnet.id.startsWith('SUB-')
          ? parseInt(subnet.id.replace('SUB-', ''))
          : subnet.id;

        console.log(`Generating IPs for subnet ${subnet.id} (ID: ${subnetId}), force=${force}`);

        // Prepare request data
        const requestData = {
          subnet_id: subnetId,
          force: force,
          exclude_gateway: true, // Don't generate IPs for the gateway address
          token: localStorage.getItem('admin_token')
        };

        console.log('Request data:', requestData);

        // Make the API request
        const response = await axios.post(
          `${API_URL}/api_admin_subnets.php?f=generate_subnet_ips`,
          requestData,
          {
            timeout: 60000, // 60 seconds timeout (increased from 30)
            headers: {
              'Content-Type': 'application/json'
            }
          }
        );

        console.log('Full API response:', response);

        // Close the pending toast
        toast.dismiss(pendingToast);

        // Check if we need confirmation to regenerate IPs
        if (response.data && response.data.needs_confirmation) {
          const confirmed = window.confirm(
            `This subnet already has ${response.data.existing_ips} IP addresses. Do you want to regenerate them?`
          );

          if (confirmed) {
            // Call again with force=true
            try {
              await handleGenerateIps(subnet, true);
              resolve(); // Resolve the promise after the recursive call completes
            } catch (err) {
              reject(err); // Reject if the recursive call fails
            }
            return;
          } else {
            toast.info('IP generation cancelled');
            resolve(); // Resolve the promise even if cancelled
            return;
          }
        }

        // Check if the response indicates success
        if (response.data && response.data.success) {
          // Success! Show confirmation and update UI
          toast.success(`Successfully generated ${response.data.ip_count} IP addresses`);

          // Refresh subnet data
          await fetchSubnets();

          // If we have a selected subnet, update its data
          if (selectedSubnet && selectedSubnet.id === subnet.id) {
            const updatedSubnet = subnets.find(s => s.id === subnet.id);
            if (updatedSubnet) {
              setSelectedSubnet(updatedSubnet);
            }
          }

          // Trigger a refresh of the subnet tree component's IP addresses
          setIpRefreshTrigger(prev => prev + 1);

          // Resolve the promise with success
          resolve(response.data);
        } else {
          // API returned but without success flag
          const errorMsg = response.data?.error || 'Unknown error occurred';
          console.error('API returned without success:', response.data);
          toast.error(`Error generating IPs: ${errorMsg}`);

          // Reject the promise with the error
          reject(new Error(errorMsg));
        }
      } catch (err) {
        // Close the pending toast if it exists
        toast.dismiss();

        console.error('Exception in Generate IPs:', {
          message: err.message,
          response: err.response?.data,
          status: err.response?.status,
          stack: err.stack
        });

        // Provide specific error message based on error type
        if (err.response) {
          // Server responded with an error status
          const serverError = err.response.data?.error || err.response.statusText || 'Server error';
          toast.error(`Server error: ${serverError}`);
        } else if (err.request) {
          // Request was made but no response received
          toast.error('No response from server. The request may have timed out or the server is not responding.');
          console.error('Request made but no response received:', err.request);
        } else {
          // Other errors
          toast.error(`Error: ${err.message || 'Unknown error occurred'}`);
        }

        // Reject the promise with the error
        reject(err);
      }
    });
  };

  // IP allocation modal handlers
  const openAllocateIpModal = (subnet, ipAddress) => {
    setSelectedSubnet(subnet);
    setSelectedIpAddress(ipAddress);
    setIsAllocateIpModalOpen(true);
  };

  const closeAllocateIpModal = () => {
    setIsAllocateIpModalOpen(false);
    setSelectedIpAddress(null);
  };

  // Allocation history modal handlers
  const openAllocationHistoryModal = (serverId, serverType) => {
    setSelectedServerId(serverId);
    setSelectedServerType(serverType);
    setIsAllocationHistoryModalOpen(true);
  };

  const closeAllocationHistoryModal = () => {
    setIsAllocationHistoryModalOpen(false);
    setSelectedServerId(null);
    setSelectedServerType(null);
  };

  // Allocation stats modal handlers
  const openAllocationStatsModal = () => {
    setIsAllocationStatsModalOpen(true);
  };

  const closeAllocationStatsModal = () => {
    setIsAllocationStatsModalOpen(false);
  };

  // Handle IP allocation
  const handleAllocateIp = async (data) => {
    try {
      console.log('Allocate IP Request Data:', data);

      const response = await axios.post(
        `${API_URL}/api_admin_subnets.php?f=allocate_ip`,
        {
          ...data,
          token: localStorage.getItem('admin_token')
        },
        {
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Full Allocate IP Response:', response);

      if (response.data.success) {
        // Show success message
        const successMessage = response.data.server_updated
          ? 'IP address allocated and server IPMI updated successfully'
          : 'IP address allocated successfully';
        toast.success(successMessage);

        // Refresh subnet data
        await fetchSubnets();

        // Trigger a refresh of the subnet tree component's IP addresses
        console.log('Triggering IP refresh after allocation');
        setIpRefreshTrigger(prev => {
          const newValue = prev + 1;
          console.log(`Setting ipRefreshTrigger from ${prev} to ${newValue}`);
          return newValue;
        });

        // Update the subnet status in the UI to reflect it's now unavailable
        if (data && data.subnet_id) {
          const subnetId = typeof data.subnet_id === 'string' && data.subnet_id.startsWith('SUB-')
            ? data.subnet_id
            : `SUB-${String(data.subnet_id).padStart(4, '0')}`;

          console.log(`Updating subnet ${subnetId} status to Unavailable`);

          // If we have parent subnets from the API response, use those
          if (response.data.parent_subnets && response.data.parent_subnets.length > 0) {
            console.log(`API returned ${response.data.parent_subnets.length} parent subnets to mark as unavailable`);

            // Update the allSubnets state to mark parent subnets as unavailable, but NOT the subnet itself
            // We only mark parent subnets as unavailable, not the subnet itself
            let updatedSubnets = allSubnets.map(s => {
              // Check if this subnet is a parent of the subnet with the allocated IP
              const isParent = response.data.parent_subnets.some(
                parent => parent.formatted_id === s.id || parent.id === s.id
              );

              if (isParent) {
                console.log(`Marking parent subnet ${s.id} as unavailable`);
                return { ...s, status: 'Unavailable' };
              }
              return s;
            });

            // Apply our enhanced parent subnet detection to catch any missed parents
            const processedSubnets = markParentSubnetsAsUnavailable(updatedSubnets);

            // Run the function a second time to catch any cascading effects
            const finalSubnets = markParentSubnetsAsUnavailable(processedSubnets);

            setAllSubnets(finalSubnets);
          } else {
            // Fallback to the old method if API doesn't return parent subnets
            const subnet = allSubnets.find(s => s.id === subnetId);
            if (subnet) {
              // Create a copy of allSubnets without marking the subnet itself as unavailable
              // We want to allow allocating multiple IPs from the same subnet
              const updatedSubnets = [...allSubnets];

              // Use our helper function to mark parent subnets as unavailable
              const processedSubnets = markParentSubnetsAsUnavailable(updatedSubnets);

              // Run the function a second time to catch any cascading effects
              const finalSubnets = markParentSubnetsAsUnavailable(processedSubnets);
              setAllSubnets(finalSubnets);
            }
          }

          // After updating the state, trigger a refresh of the tree to ensure all parent subnets are properly marked
          fetchAllSubnetsForTree();
        }

        closeAllocateIpModal();
      } else {
        console.error('Allocate IP Error:', response.data);
        toast.error(response.data.error || 'Failed to allocate IP address');
      }
    } catch (err) {
      console.error('Detailed Error Allocating IP:', {
        message: err.message,
        response: err.response?.data,
        status: err.response?.status,
        headers: err.response?.headers
      });

      // More specific error handling
      if (err.response && err.response.data && err.response.data.error) {
        toast.error(err.response.data.error);
      } else {
        toast.error('Error allocating IP address: ' + (err.response?.data?.error || err.message));
      }
    }
  };

  // Handle note update
  const handleUpdateNote = async (subnetId, newNote) => {
    try {
      const response = await axios.post(`${API_URL}/api_admin_subnets.php?f=update_note`, {
        subnet_id: subnetId,
        note: newNote,
        token: localStorage.getItem('admin_token')
      });

      if (response.data.success) {
        toast.success('Note updated successfully');
        
        // Update the subnets state
        setSubnets(prevSubnets =>
          prevSubnets.map(s => {
            if (s.id === subnetId) {
              return { ...s, note: newNote };
            }
            return s;
          })
        );

        // Update allSubnets state
        setAllSubnets(prevSubnets =>
          prevSubnets.map(s => {
            if (s.id === subnetId) {
              return { ...s, note: newNote };
            }
            return s;
          })
        );

        // Update selected subnet if it's the one being edited
        if (selectedSubnet && selectedSubnet.id === subnetId) {
          setSelectedSubnet(prev => ({ ...prev, note: newNote }));
        }

        // Reset editing state
        setIsEditingNote(false);
        setEditingNote('');
      } else {
        toast.error(response.data.error || 'Failed to update note');
      }
    } catch (err) {
      console.error('Error updating note:', err);
      toast.error('Error updating note: ' + (err.response?.data?.error || err.message));
    }
  };

  // Handle IP deallocation
  const handleDeallocateIp = async (subnet, ipAddress) => {
    if (!window.confirm(`Are you sure you want to deallocate IP address ${ipAddress}?`)) {
      return;
    }

    try {
      const subnetId = typeof subnet.id === 'string' && subnet.id.startsWith('SUB-')
        ? parseInt(subnet.id.replace('SUB-', ''))
        : subnet.id;

      const response = await axios.post(`${API_URL}/api_admin_subnets.php?f=deallocate_ip`, {
        subnet_id: subnetId,
        ip_address: ipAddress,
        token: localStorage.getItem('admin_token')
      });

      if (response.data.success) {
        // Show success message
        const successMessage = response.data.server_updated
          ? 'IP address deallocated and server IPMI updated successfully'
          : 'IP address deallocated successfully';
        toast.success(successMessage);

        // Refresh subnet data
        await fetchSubnets();

        // Trigger a refresh of the subnet tree component's IP addresses
        console.log('Triggering IP refresh after deallocation');
        setIpRefreshTrigger(prev => {
          const newValue = prev + 1;
          console.log(`Setting ipRefreshTrigger from ${prev} to ${newValue}`);
          return newValue;
        });

        // Check if this was the last allocated IP in the subnet
        try {
          // Get the subnet ID in the correct format
          const subnetId = typeof subnet.id === 'string' && subnet.id.startsWith('SUB-')
            ? subnet.id
            : `SUB-${String(subnet.id).padStart(4, '0')}`;

          // Check if there are any remaining allocated IPs
          const ipResponse = await axios.post(
            `${API_URL}/api_admin_subnets.php?f=get_subnet_ips`,
            {
              subnet_id: subnet.id,
              token: localStorage.getItem('admin_token')
            }
          );

          if (ipResponse.data && ipResponse.data.success) {
            const allocatedIps = ipResponse.data.ips.filter(ip =>
              ip.is_used === '1' || ip.is_used === 1
            );

            console.log(`Subnet ${subnetId} has ${allocatedIps.length} allocated IPs remaining`);

            // If no more allocated IPs, update the subnet and its parents' status
            if (allocatedIps.length === 0) {
              // Find the subnet object
              const subnetObj = allSubnets.find(s => s.id === subnetId);

              if (subnetObj) {
                // Get all parent subnets
                const parentSubnets = findParentSubnets(subnetObj, allSubnets);
                console.log(`Found ${parentSubnets.length} parent subnets to update after deallocation`);

                // Check if any parent has other children with allocated IPs
                const parentsToUpdate = parentSubnets.filter(parent => {
                  // Find all children of this parent
                  const children = allSubnets.filter(s => {
                    const parents = findParentSubnets(s, allSubnets);
                    return parents.some(p => p.id === parent.id);
                  });

                  // Check if any child other than the current subnet has allocated IPs
                  const hasOtherChildrenWithAllocatedIps = children.some(child =>
                    child.id !== subnetId &&
                    child.status === 'Unavailable'
                  );

                  return !hasOtherChildrenWithAllocatedIps;
                });

                // Update the subnet and eligible parents to Available
                const updatedSubnets = allSubnets.map(s => {
                  if (s.id === subnetId || parentsToUpdate.some(p => p.id === s.id)) {
                    return { ...s, status: 'Available' };
                  }
                  return s;
                });

                setAllSubnets(updatedSubnets);
              }
            }
          }
        } catch (err) {
          console.error('Error checking remaining allocated IPs:', err);
        }
      } else {
        console.error('Deallocate IP Error:', response.data);
        toast.error(response.data.error || 'Failed to deallocate IP address');
      }
    } catch (err) {
      console.error('Error deallocating IP:', err);
      toast.error('Error deallocating IP address: ' + (err.response?.data?.error || err.message));
    }
  };

  const handleDivideSubnet = async (formData) => {
    // Create a properly formatted request payload
    const requestData = {
      subnet_id: formData.subnet_id,
      new_subnet_size: formData.new_subnet_size,
      generate_ips: formData.generate_ips || false, // Use the value from the form or default to false
      token: localStorage.getItem('admin_token')
    };

    console.log('Divide Subnet Request Data:', requestData);

    try {
      // Show a pending toast to indicate the request is being processed
      const pendingToast = toast.info('Processing subnet division...', { autoClose: false });

      // Make the API request with explicit error handling
      const response = await axios.post(
        `${API_URL}/api_admin_subnets.php?f=divide_subnet`,
        requestData,
        {
          // Add timeout and headers for better debugging
          timeout: 30000, // 30 seconds timeout
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      // Log the full response for debugging
      console.log('Full Divide Subnet Response:', response);

      // Close the pending toast
      toast.dismiss(pendingToast);

      // Check if the response indicates success
      if (response.data && response.data.success) {
        // Success! Show confirmation and update UI
        toast.success(`Subnet divided successfully into ${response.data.new_subnet_ids.length} smaller subnets`);

        // Only call fetchSubnets once and await it to ensure data is fully refreshed
        await fetchSubnets();

        closeDivideSubnetModal(); // Close the modal
      } else {
        // API returned but without success flag
        const errorMsg = response.data?.error || 'Unknown error occurred';
        console.error('API returned without success:', response.data);
        toast.error(`Error dividing subnet: ${errorMsg}`);
      }
    } catch (err) {
      // Detailed error logging for all possible error scenarios
      console.error('Exception in Divide Subnet:', {
        message: err.message,
        response: err.response?.data,
        status: err.response?.status,
        stack: err.stack
      });

      // Provide specific error message based on error type
      if (err.response) {
        // Server responded with an error status
        const serverError = err.response.data?.error || err.response.statusText || 'Server error';
        toast.error(`Server error: ${serverError}`);
      } else if (err.request) {
        // Request was made but no response received
        toast.error('No response from server. Please check your network connection.');
      } else {
        // Other errors
        toast.error(`Error: ${err.message}`);
      }
    }
  };

  // Helper functions
  const renderStatusBadge = (status, assignedTo, subnet = null) => {
    const badgeClasses = {
      'Available': 'bg-green-100 text-green-800',
      'Assigned': 'bg-blue-100 text-blue-800',
      'Allocated': 'bg-purple-100 text-purple-800',
      'Full': 'bg-red-100 text-red-800',
      'Unavailable': 'bg-orange-100 text-orange-800', // New status
      'Partially Available': 'bg-green-100 text-green-800',
      'Reserved': 'bg-yellow-100 text-yellow-800',
      'Deprecated': 'bg-red-100 text-red-800',
      'Active': 'bg-green-100 text-green-800'
    };

    const icons = {
      'Available': <CheckCircle className="w-4 h-4 mr-1" />,
      'Assigned': <Link className="w-4 h-4 mr-1" />,
      'Allocated': <Tag className="w-4 h-4 mr-1" />,
      'Full': <XCircle className="w-4 h-4 mr-1" />,
      'Unavailable': <AlertCircle className="w-4 h-4 mr-1" />, // New status
      'Partially Available': <AlertCircle className="w-4 h-4 mr-1" />,
      'Reserved': <AlertCircle className="w-4 h-4 mr-1" />,
      'Deprecated': <XCircle className="w-4 h-4 mr-1" />,
      'Active': <CheckCircle className="w-4 h-4 mr-1" />
    };

    // If status is Assigned and we have assigned server info, display it
    let displayText = status;
    if (status === 'Assigned' && assignedTo && assignedTo !== 'Unassigned') {
      displayText = assignedTo;
    } else if (status === 'Allocated' && subnet && subnet.manual_alocation) {
      // For allocated subnets, show the manual allocation description (truncated if needed)
      displayText = subnet.manual_alocation.length > 20 ?
        subnet.manual_alocation.substring(0, 20) + '...' :
        subnet.manual_alocation;
    } else if (status === 'Unavailable') {
      // Check if the subnet has allocated IPs
      if (subnet && subnet.usedIPs && subnet.usedIPs > 0) {
        displayText = `Has Allocated IPs (${subnet.usedIPs})`;
      } else {
        displayText = 'Parent of Allocated Subnet';
      }
    }

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center w-fit ${badgeClasses[status] || 'bg-gray-100 text-gray-800'}`}>
        {icons[status] || <AlertCircle className="w-3 h-3 mr-1" />}
        {displayText}
      </span>
    );
  };



  // Format IP address CIDR notation
  const formatCidr = (cidr) => {
    const [subnet, mask] = cidr.split('/');
    return (
      <div className="font-mono">
        {subnet}<span className="text-indigo-700">/{mask}</span>
      </div>
    );
  };

  // Only paginate the main subnets
  const paginatedSubnets = mainSubnetsArray.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Use renderTrigger to force re-renders when needed
  // We'll use this in a data attribute to ensure it's included in the render cycle
  const forceRender = renderTrigger;

  // Memoized list of the selected subnet and all its descendant subnets
  const relatedSubnets = useMemo(() => {
    if (!selectedSubnet) return [];

    if (!selectedSubnet.cidr) return [selectedSubnet];

    // Step 1: find ancestors of selected subnet
    const ancestors = allSubnets.filter(
      (s) => s.cidr && isCidrParent(s.cidr, selectedSubnet.cidr)
    );

    // Helper to decide if subnet belongs to the family (selected + ancestors tree)
    const belongsToFamily = (s) => {
      if (!s.cidr) return false;

      // Selected itself
      if (s.id === selectedSubnet.id) return true;

      // Descendant of selected
      if (isCidrParent(selectedSubnet.cidr, s.cidr)) return true;

      // Ancestor of selected
      if (isCidrParent(s.cidr, selectedSubnet.cidr)) return true;

      // Sibling / cousin: share an ancestor with selected
      return ancestors.some((ancestor) => isCidrParent(ancestor.cidr, s.cidr));
    };

    return allSubnets.filter(belongsToFamily);
  }, [selectedSubnet, allSubnets]);

  // Helper: determine if a subnet can still be divided (max mask /30)
  const canSubnetBeDivided = (subnet) => {
    if (!subnet || !subnet.cidr) return false;
    const parts = subnet.cidr.split('/');
    if (parts.length !== 2) return false;
    const mask = parseInt(parts[1], 10);
    if (isNaN(mask)) return false;
    // /30 (mask == 30) is the smallest subnet we allow to divide; anything greater (31,32) cannot be divided
    return mask <= 30;
  };

  // NEW HELPER: determine if a subnet is eligible for IP generation (IPv4 and mask <= /30)
  const canGenerateIpsForSubnet = (subnet) => {
    if (!subnet || !subnet.cidr) return false;
    // Only IPv4 subnets are eligible
    if (!subnet.cidr.includes('.')) return false;
    const parts = subnet.cidr.split('/');
    if (parts.length !== 2) return false;
    const mask = parseInt(parts[1], 10);
    if (isNaN(mask)) return false;
    // Do not allow IP generation for /31 or /32 (or any mask > 30)
    return mask <= 30;
  };

  const handleGenerateChildren = async (subnet) => {
    if (!subnet) return;

    // parse subnet size
    const parts = subnet.cidr.split('/');
    if (parts.length !== 2) {
      toast.error('Invalid subnet CIDR');
      return;
    }
    const currentSize = parseInt(parts[1], 10);
    if (isNaN(currentSize) || currentSize >= 31) {
      toast.error('Subnet cannot be further divided');
      return;
    }

    const newSize = currentSize + 1;

    await handleDivideSubnet({
      subnet_id: subnet.id,
      new_subnet_size: newSize,
      generate_ips: false
    });
  };

  // Handle deleting an unused IP address (row removal)
  const handleDeleteIp = async (subnet, ipAddress) => {
    if (!window.confirm(`Delete unused IP address ${ipAddress}? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await axios.post(`${API_URL}/api_admin_subnets.php?f=delete_ip`, {
        ip_address: ipAddress,
        token: localStorage.getItem('admin_token')
      });

      if (response.data && response.data.success) {
        toast.success('IP address deleted successfully');

        // Refresh subnet data
        await fetchSubnets();

        // Trigger tree refresh
        setIpRefreshTrigger(prev => prev + 1);
      } else {
        toast.error(response.data.error || 'Failed to delete IP address');
      }
    } catch (err) {
      console.error('Error deleting IP:', err);
      toast.error('Error deleting IP address: ' + (err.response?.data?.error || err.message));
    }
  };

  return (
    <div className="flex h-screen bg-gray-100" data-render-key={forceRender}>
      {/* Sidebar */}
      <Sidebar
        sidebarCollapsed={sidebarCollapsed}
        activeMenu="Subnets"
        navigateTo={navigateTo}
        toggleSidebar={toggleSidebar}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col bg-gray-100">
        {/* Top Menu */}
        <TopMenu toggleSidebar={toggleSidebar} />

        {/* Subnets Content */}
        <div className="p-6 space-y-6 overflow-auto">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-800">Subnets Management</h1>
            <div className="flex items-center space-x-2">
              <div className="text-sm text-gray-500">Last updated: {lastUpdated}</div>
              <button
                onClick={handleRefreshData}
                className="p-2 text-gray-500 hover:text-indigo-700 rounded-full hover:bg-gray-100"
                disabled={isLoading}
              >
                <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              </button>
              <div className="flex items-center space-x-2">
                <button
                  className="bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center"
                  onClick={openAllocationStatsModal}
                  disabled={isLoading}
                  title="View allocation statistics"
                >
                  <BarChart3 className="w-4 h-4 mr-1" />
                  Stats
                </button>
                <button
                  className="bg-indigo-700 hover:bg-indigo-800 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center"
                  onClick={openAddSubnetModal}
                  disabled={isLoading}
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Add New Subnet
                </button>
              </div>
            </div>
          </div>



          {/* Subnets Filter and Search */}
          <div className="bg-white border border-gray-200 shadow-sm rounded-md">
            <div className="p-4 border-b flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div className="flex flex-wrap gap-2">
              <div className="relative">
  <select
    className="pl-3 pr-8 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 appearance-none"
    value={selectedStatus}
    onChange={(e) => handleStatusFilter(e.target.value)}
    disabled={isLoading}
  >
    <option value="All">All Status</option>
    <option value="Available">Available</option>
    <option value="Assigned">Assigned</option>
    <option value="Allocated">Allocated</option>
    <option value="Unavailable">Unavailable</option>
    <option value="Full">Full</option>
    {/* Include any other statuses as needed */}
  </select>
  <Filter className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
</div>
                <div className="relative">
                  <select
                    className="pl-3 pr-8 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 appearance-none"
                    value={selectedCategory}
                    onChange={(e) => handleCategoryFilter(e.target.value)}
                    disabled={isLoading}
                  >
                    {uniqueCategories.map(category => (
                      <option key={category} value={category}>{category === 'All' ? 'All Categories' : category}</option>
                    ))}
                  </select>
                  <Filter className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                </div>
                <div className="relative">
                  <select
                    className="pl-3 pr-8 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 appearance-none"
                    value={selectedLocation}
                    onChange={(e) => handleLocationFilter(e.target.value)}
                    disabled={isLoading}
                  >
                    {uniqueLocations.map(location => (
                      <option key={location} value={location}>{location === 'All' ? 'All Locations' : location}</option>
                    ))}
                  </select>
                  <Filter className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                </div>
              </div>
              <div className="relative w-full sm:w-64">
                <input
                  type="text"
                  placeholder="Search subnets..."
                  className="pl-8 pr-3 py-2 border rounded-md text-sm w-full focus:outline-none focus:ring-2 focus:ring-indigo-200 search-input"
                  value={searchQuery}
                  onChange={handleSearch}
                  disabled={isLoading}
                />
                <Search className="absolute left-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
              </div>
            </div>

            {/* Error display */}
            {error && (
              <div className="p-4 bg-red-50 text-red-700 border-b border-red-100">
                <div className="flex items-center">
                  <AlertCircle className="w-5 h-5 mr-2" />
                  <span>{error}</span>
                </div>
              </div>
            )}

            {/* Loading indicator */}
            {isLoading && (
              <div className="p-4 text-center text-gray-500">
                <div className="inline-block animate-spin mr-2">
                  <RefreshCw className="w-5 h-5" />
                </div>
                Loading subnet data...
              </div>
            )}

            {/* Subnets Table - Now only showing main subnets */}
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="text-gray-500 text-xs border-b">

                    <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('cidr')}>
                      SUBNET {getSortIcon('cidr')}
                    </th>

                    <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('status')}>
                      STATUS {getSortIcon('status')}
                    </th>
                    <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('location')}>
                      LOCATION {getSortIcon('location')}
                    </th>
                    <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('category')}>
                      CATEGORY {getSortIcon('category')}
                    </th>
                    <th className="p-4 text-left font-medium cursor-pointer" onClick={() => handleSort('note')}>
                      NOTE {getSortIcon('note')}
                    </th>
                    <th className="p-4 text-left font-medium">ACTIONS</th>
                  </tr>
                </thead>
                <tbody>
                {!isLoading && mainSubnets.length > 0 ? (
                    paginatedSubnets.map((subnet, index) => (
                      <tr
                        key={subnet.id}
                        className={`border-b ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-indigo-50 cursor-pointer`}
                        onClick={() => handleSubnetClick(subnet)}
                      >
                       
                        <td className="p-4 text-gray-700">{formatCidr(subnet.cidr)}</td>
      

           
                        <td className="p-4">{renderStatusBadge(subnet.status, subnet.assignedTo, subnet)}</td>
                        <td className="p-4 text-gray-700">{subnet.location}</td>
                        <td className="p-4 text-gray-700">{subnet.category}</td>
                        <td className="p-4 text-gray-700 max-w-xs">
                          <div className="truncate" title={subnet.note || 'No note'}>
                            {subnet.note ? (subnet.note.length > 50 ? subnet.note.substring(0, 50) + '...' : subnet.note) : '-'}
                          </div>
                        </td>
                        <td className="p-4" onClick={(e) => e.stopPropagation()}>
  <div className="flex space-x-2">


    {/* Only show Divide button for Available subnets that don't have children */}
    {subnet.status === 'Available' && !checkSubnetHasChildren(subnet) && canSubnetBeDivided(subnet) && (
      <button
        className="p-1 text-gray-500 hover:text-indigo-700 transition-colors"
        title="Divide Subnet"
        onClick={(e) => {
          e.stopPropagation();
          openDivideSubnetModal(subnet);
        }}
      >
        <Divide className="w-4 h-4" />
      </button>
    )}

    {/* Generate IPs button - show only for Available IPv4 subnets */}
    {subnet.status === 'Available' && canGenerateIpsForSubnet(subnet) && (
      <button
        className="p-1 text-gray-500 hover:text-indigo-700 transition-colors"
        title="Generate IP Addresses"
        onClick={(e) => {
          e.stopPropagation();
          handleGenerateIps(subnet);
        }}
      >
        <Network className="w-4 h-4" />
      </button>
    )}

    {/* Generate missing child button (when exactly one child exists) */}
    {(subnet.status === 'Available' || subnet.status === 'Unavailable') && getDirectChildCount(subnet) === 1 && canSubnetBeDivided(subnet) && (
      <button
        className="p-1 text-gray-500 hover:text-indigo-700 transition-colors"
        title="Generate Missing Child Subnet"
        onClick={(e) => {
          e.stopPropagation();
          handleGenerateChildren(subnet);
        }}
      >
        <Network className="w-4 h-4" />
      </button>
    )}

 

    {/* Delete button - conditionally disabled for non-deletable subnets */}
    {subnet.status === 'Allocated' || subnet.status === 'Assigned' || subnet.status === 'Unavailable' ? (
      <button
        className="p-1 text-gray-400 cursor-not-allowed transition-colors"
        title={
          subnet.status === 'Allocated' ? 'Cannot delete: Subnet is allocated/reserved. Please deallocate first.' :
          subnet.status === 'Assigned' ? 'Cannot delete: Subnet is assigned to a server. Please unassign first.' :
          'Cannot delete: Subnet has allocated children or is part of an allocated hierarchy.'
        }
        disabled
      >
        <Trash2 className="w-4 h-4" />
      </button>
    ) : (
      <button
        className="p-1 text-gray-500 hover:text-red-700 transition-colors"
        title="Delete Subnet"
        onClick={(e) => {
          e.stopPropagation();
          handleDeleteSubnet(subnet.id);
        }}
      >
        <Trash2 className="w-4 h-4" />
      </button>
    )}
  </div>
</td>
                      </tr>
                    ))
                  ) : (
                    !isLoading && (
                      <tr>
                        <td colSpan="6" className="p-4 text-center text-gray-500">
                          No subnets found matching your criteria
                        </td>
                      </tr>
                    )
                  )}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {!isLoading && mainSubnets.length > 0 && (
              <Pagination
                currentPage={currentPage}
                totalPages={Math.ceil(mainSubnets.length / itemsPerPage)}
                totalItems={mainSubnets.length}
                itemsPerPage={itemsPerPage}
                onPageChange={handlePageChange}
                onPreviousPage={handlePreviousPage}
                onNextPage={handleNextPage}
              />
            )}
          </div>
        </div>
      </div>

      {/* Subnet Details Modal - Modified to be more compact */}
      {selectedSubnet && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg max-w-2xl w-full max-h-[85vh] overflow-y-auto">
            <div className="p-3 border-b flex justify-between items-center">
              <h2 className="text-lg font-bold text-gray-800">Subnet Details</h2>
              <button
                onClick={closeSubnetDetails}
                className="text-gray-500 hover:text-gray-700"
              >
                <XCircle className="w-5 h-5" />
              </button>
            </div>
            <div className="p-4 space-y-4">
              {/* Subnet Header - More compact */}
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center border-b pb-3">
            <div>
              <div className="text-xl font-bold text-gray-800">{selectedSubnet.cidr}</div>

            </div>
            {renderStatusBadge(selectedSubnet.status, selectedSubnet.assignedTo, selectedSubnet)}
          </div>

              {/* Subnet Information - More compact */}
              <div>
                <h3 className="text-md font-bold mb-1">Subnet Information</h3>
                <div className="bg-gray-50 rounded-md p-3">
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3 text-sm">

                    <div>
                      <div className="text-xs text-gray-500">Location</div>
                      <div className="font-medium">{selectedSubnet.location}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">Gateway</div>
                      <div className="font-medium font-mono">{selectedSubnet.gateway}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">Category</div>
                      <div className="font-medium">{selectedSubnet.category}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">Assigned To</div>
                      <div className="font-medium">{selectedSubnet.assignedTo}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">Last Updated</div>
                      <div className="font-medium">{selectedSubnet.lastUpdated}</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Server Actions - Show when subnet is assigned to a server */}
              {selectedSubnet.status === 'Assigned' && selectedSubnet.assigned_server_id && (
                <div>
                  <h3 className="text-md font-bold mb-2">Server Actions</h3>
                  <div className="bg-blue-50 rounded-md p-3">
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-gray-700">
                        <div className="font-medium">Server ID: {selectedSubnet.assigned_server_id}</div>
                        <div className="text-xs text-gray-500">
                          Type: {selectedSubnet.assigned_server_id >= 1000000 ? 'Dedicated' : 'Blade'} Server
                        </div>
                      </div>
                      <button
                        onClick={() => openAllocationHistoryModal(
                          selectedSubnet.assigned_server_id,
                          selectedSubnet.assigned_server_id >= 1000000 ? 'dedicated' : 'blade'
                        )}
                        className="bg-indigo-600 hover:bg-indigo-700 text-white px-3 py-2 rounded-md text-sm font-medium flex items-center"
                        title="View allocation history for this server"
                      >
                        <Clock className="w-4 h-4 mr-1" />
                        History
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {/* Note Section */}
              <div>
                <div className="flex justify-between items-center mb-1">
                  <h3 className="text-md font-bold">Note</h3>
                  <button
                    onClick={() => {
                      setIsEditingNote(!isEditingNote);
                      setEditingNote(selectedSubnet.note || '');
                    }}
                    className="p-1 text-gray-500 hover:text-indigo-700 transition-colors"
                    title="Edit Note"
                  >
                    <Edit className="w-4 h-4" />
                  </button>
                </div>
                {isEditingNote ? (
                  <div className="space-y-2">
                    <textarea
                      value={editingNote}
                      onChange={(e) => setEditingNote(e.target.value)}
                      className="w-full p-2 border rounded-md text-sm resize-none focus:outline-none focus:ring-2 focus:ring-indigo-200"
                      rows={3}
                      placeholder="Enter note..."
                    />
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleUpdateNote(selectedSubnet.id, editingNote)}
                        className="px-3 py-1 bg-indigo-700 text-white text-xs rounded-md hover:bg-indigo-800 flex items-center"
                      >
                        <Save className="w-3 h-3 mr-1" />
                        Save
                      </button>
                      <button
                        onClick={() => {
                          setIsEditingNote(false);
                          setEditingNote('');
                        }}
                        className="px-3 py-1 bg-gray-300 text-gray-700 text-xs rounded-md hover:bg-gray-400 flex items-center"
                      >
                        <X className="w-3 h-3 mr-1" />
                        Cancel
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="bg-gray-50 rounded-md p-3">
                    <p className="text-sm text-gray-700 whitespace-pre-wrap">
                      {selectedSubnet.note || 'No note added yet.'}
                    </p>
                  </div>
                )}
              </div>


              {/* Manual Allocation - More compact */}
              {selectedSubnet.status === 'Allocated' && selectedSubnet.manual_alocation && (
                <div>
                  <h3 className="text-md font-bold mb-1">Allocation Purpose</h3>
                  <div className="bg-gray-50 rounded-md p-3">
                    <p className="text-xs text-gray-700 whitespace-pre-wrap">
                      {selectedSubnet.manual_alocation}
                    </p>
                  </div>
                </div>
              )}

              {/* Subnet Hierarchy - More compact */}
              <div>
  <h3 className="text-xs font-medium text-gray-700 mb-1 flex items-center">
    <Network className="w-4 h-4 mr-1 text-indigo-700" />
    Subnet Hierarchy
  </h3>
  <CompactSubnetTree
    subnets={relatedSubnets} // Show only the selected subnet and its children
    currentSubnetId={selectedSubnet.id}
    onSubnetSelect={(subnet) => {
      closeSubnetDetails();
      setSelectedSubnet(subnet);
    }}
    onGenerateIps={handleGenerateIps}
    onAllocateIp={null}
    onDeallocateIp={handleDeallocateIp}
    onDeleteIp={handleDeleteIp}
    refreshTrigger={ipRefreshTrigger} // Pass the refresh trigger
  />
</div>



{/* Updated Subnet Actions in the detail modal */}
<div className="flex flex-wrap gap-2 justify-end pt-3 border-t">
  {/* Only show Divide button for Available subnets that don't have children */}
  {selectedSubnet.status === 'Available' && !checkSubnetHasChildren(selectedSubnet) && canSubnetBeDivided(selectedSubnet) && (
    <button
      className="px-3 py-1 border border-gray-300 rounded-md text-xs text-gray-700 flex items-center hover:bg-gray-50"
      onClick={() => {
        closeSubnetDetails();
        openDivideSubnetModal(selectedSubnet);
      }}
    >
      <Divide className="w-3 h-3 mr-1" />
      Divide Subnet
    </button>
  )}

  {/* Generate IPs button - show only for Available IPv4 subnets */}
  {selectedSubnet.status === 'Available' && canGenerateIpsForSubnet(selectedSubnet) && (
    <button
      className="px-3 py-1 border border-gray-300 rounded-md text-xs text-gray-700 flex items-center hover:bg-gray-50"
      onClick={() => handleGenerateIps(selectedSubnet)}
    >
      <Network className="w-3 h-3 mr-1" />
      Generate IPs
    </button>
  )}

  {selectedSubnet.status === 'Available' ? (
    <>

      <button
        className="px-3 py-1 border border-gray-300 rounded-md text-xs text-gray-700 flex items-center hover:bg-gray-50"
        onClick={() => {
          closeSubnetDetails();
          openAllocateSubnetModal(selectedSubnet);
        }}
      >
        <Tag className="w-3 h-3 mr-1" />
        Reserve Subnet
      </button>
      {/* Delete Subnet button */}
      <button
        className="px-3 py-1 border border-red-300 text-red-600 rounded-md text-xs flex items-center hover:bg-red-50"
        onClick={() => {
          closeSubnetDetails();
          handleDeleteSubnet(selectedSubnet.id);
        }}
      >
        <Trash2 className="w-3 h-3 mr-1" />
        Delete Subnet
      </button>
      {/* Generate Missing Child button */}
      {getDirectChildCount(selectedSubnet) === 1 && canSubnetBeDivided(selectedSubnet) && (
        <button
          className="px-3 py-1 border border-gray-300 rounded-md text-xs text-gray-700 flex items-center hover:bg-gray-50"
          onClick={() => handleGenerateChildren(selectedSubnet)}
        >
          <Network className="w-3 h-3 mr-1" />
          Generate Child
        </button>
      )}
    </>
  )  : selectedSubnet.status === 'Allocated' ? (
    <button
      className="px-3 py-1 border border-gray-300 rounded-md text-xs text-gray-700 flex items-center hover:bg-gray-50"
      onClick={async () => {
        // Don't close the modal
        try {
          await handleDeallocateSubnet(selectedSubnet.id);
          // After successful deallocate, update the selected subnet data
          const updatedSubnets = subnets.map(s => {
            if (s.id === selectedSubnet.id) {
              return {
                ...s,
                status: 'Available',
                is_allocated: 0,
                manual_alocation: null
              };
            }
            return s;
          });
          setSubnets(updatedSubnets);

          // Update the selected subnet in the modal
          setSelectedSubnet(prev => ({
            ...prev,
            status: 'Available',
            is_allocated: 0,
            manual_alocation: null
          }));
        } catch (error) {
          console.error('Error deallocating subnet:', error);
        }
      }}
    >
      <Unlink className="w-3 h-3 mr-1" />
      Deallocate Subnet
    </button>
  ) : selectedSubnet.status === 'Unavailable' ? (
    getDirectChildCount(selectedSubnet) === 1 && canSubnetBeDivided(selectedSubnet) ? (
      <button
        className="px-3 py-1 border border-gray-300 rounded-md text-xs text-gray-700 flex items-center hover:bg-gray-50"
        onClick={() => handleGenerateChildren(selectedSubnet)}
      >
        <Network className="w-3 h-3 mr-1" />
        Generate Missing Child
      </button>
    ) : (
      <div className="px-3 py-1 bg-orange-50 border border-orange-200 rounded-md text-xs text-orange-800 flex items-center">
        <AlertCircle className="w-3 h-3 mr-1" />
        Cannot modify: This subnet has allocated or assigned parent/child subnets
      </div>
    )
  ) : (
    <button
      className="px-3 py-1 border border-gray-300 rounded-md text-xs text-gray-400 cursor-not-allowed flex items-center"
      disabled
    >
      <AlertCircle className="w-3 h-3 mr-1" />
      Cannot Modify
    </button>
  )}

</div>
            </div>
          </div>
        </div>
      )}

      {/* Add Subnet Modal */}
      {isAddModalOpen && (
        <AddSubnetModal
          onClose={closeAddSubnetModal}
          onAddSubnet={handleAddSubnet}
        />
      )}

      {/* Divide Subnet Modal */}
      {isDivideModalOpen && selectedSubnet && (
        <DivideSubnetModal
          subnet={selectedSubnet}
          onClose={closeDivideSubnetModal}
          onDivideSubnet={handleDivideSubnet}
        />
      )}

      {/* Assign Subnet Modal */}
      {isAssignModalOpen && selectedSubnet && (
        <AssignSubnetModal
          subnet={selectedSubnet}
          onClose={closeAssignSubnetModal}
          onAssignSubnet={handleAssignSubnet}
        />
      )}

      {/* Allocate Subnet Modal */}
      {isAllocateModalOpen && selectedSubnet && (
        <AllocateSubnetModal
          subnet={selectedSubnet}
          onClose={closeAllocateSubnetModal}
          onAllocateSubnet={handleAllocateSubnet}
        />
      )}

      {/* Allocate IP Modal */}
      {isAllocateIpModalOpen && selectedSubnet && selectedIpAddress && (
        <AllocateIpModal
          subnet={selectedSubnet}
          ipAddress={selectedIpAddress}
          onClose={closeAllocateIpModal}
          onAllocateIp={handleAllocateIp}
        />
      )}

      {/* Allocation History Modal */}
      {isAllocationHistoryModalOpen && selectedServerId && (
        <AllocationHistoryModal
          serverId={selectedServerId}
          serverType={selectedServerType}
          onClose={closeAllocationHistoryModal}
        />
      )}

      {/* Allocation Stats Modal */}
      {isAllocationStatsModalOpen && (
        <AllocationStatsModal
          onClose={closeAllocationStatsModal}
        />
      )}

    </div>

  );
};

export default SubnetsPage;