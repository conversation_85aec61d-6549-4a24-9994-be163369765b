import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import {
  Search,
  Plus,
  Filter,
  ArrowUp,
  ArrowDown,
  MoreVertical,
  Eye,
  Server,
  HardDrive,
  Cpu,
  EyeOff,
  Network,
  Settings,
  Wifi,
  Layers,
  Calendar,
  XCircle,
  RefreshCw,
  Edit,
  Trash2,
  Save,
  X,
  Database,
  Globe,
  MapPin,
  Activity,
  AlertTriangle,
  CheckCircle,
  Lock,
  Building,
  User,
  ChevronLeft,
  Power,
  PowerOff,
  Terminal
} from 'lucide-react';
import Sidebar from '../components/Sidebar';
import TopMenu from '../components/TopMenu';
import StorageDetectionButton from '../components/StorageDetectionButton';
import MacAddressDetectionButton from '../components/MacAddressDetectionButton';
import SensorInfoTab from '../components/SensorInfoTab';
import ServerLogsTab from '../components/ServerLogsTab';
import PowerManagement from '../components/PowerManagement';
import NetworkConnections from '../components/NetworkConnections';
import SelectSubnetModal from '../components/SelectSubnetModal';
import CountryIpSelector from '../components/CountryIpSelector';
import PXEReinstallModal from '../components/PXEReinstallModal';
import { API_URL } from '../config';
import axios from 'axios';
const ServerView = ({
  navigateTo,
  sidebarCollapsed,
  toggleSidebar
}) => {
  // Get server ID and type from URL params
  const params = useParams();
  const serverId = params.id || '';
  const serverType = params.type || 'dedicated'; // 'dedicated' or 'blade'

  // State for server data
  const [server, setServer] = useState(null);
  const [activeTab, setActiveTab] = useState('details');
  // State for UI interactions
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [lastUpdated, setLastUpdated] = useState('');
  const [visiblePasswords, setVisiblePasswords] = useState({});
  const [isSubnetSelectionModalOpen, setIsSubnetSelectionModalOpen] = useState(false);
  const [isSelectingMainSubnet, setIsSelectingMainSubnet] = useState(true);
  const [isPXEReinstallModalOpen, setIsPXEReinstallModalOpen] = useState(false);
  // NEW: Track PXE reinstall status to disable button when an installation is already running
  const [installationInProgress, setInstallationInProgress] = useState(false);
  const [checkingInstallStatus, setCheckingInstallStatus] = useState(false);
  // State for related data
  const [racks, setRacks] = useState([]);
  const [cities, setCities] = useState([]);
  const [countries, setCountries] = useState([]);
  const [cpuModels, setCpuModels] = useState([]);
  const [ramConfigurations, setRamConfigurations] = useState([]);
  const [chassis, setChassis] = useState([]);

  // State for modals
  const [showAddCpuModal, setShowAddCpuModal] = useState(false);
  const [newCpuModel, setNewCpuModel] = useState('');
  const [showAddRamModal, setShowAddRamModal] = useState(false);
  const [newRamConfig, setNewRamConfig] = useState({ size: '', description: '' });

  // Storage configurations
  const storageConfigurations = [
    { id: 1, size: 240, label: '240GB SSD' },
    { id: 2, size: 500, label: '500GB SSD' },
    { id: 3, size: 1000, label: '1TB SSD' },
    { id: 4, size: 2000, label: '2TB SSD' },
    { id: 5, size: 4000, label: '4TB SSD' },
    { id: 6, size: 6000, label: '6TB SSD' },
    { id: 7, size: 8000, label: '8TB SSD' },
    { id: 8, size: 10000, label: '10TB HDD' },
    { id: 9, size: 12000, label: '12TB HDD' }
  ];

  const handleSubnetSelection = async (subnet) => {
    try {
      // Store the previous main subnet for unallocation if we're assigning a new main subnet
      const previousMainSubnet = isSelectingMainSubnet ? server?.main_ip : null;
      
      console.log(`🔄 Subnet selection - Mode: ${isSelectingMainSubnet ? 'Main Subnet' : 'Additional Subnet'}`);
      console.log(`📋 Current state - Main IP: ${server?.main_ip}, Additional IPs: ${server?.additional_ips}`);
      console.log(`🎯 New subnet to assign: ${subnet.cidr}`);
      console.log(`🗑️ Previous main subnet to unallocate: ${previousMainSubnet || 'None'}`);
      
      // Update UI immediately (optimistic) - same as InventoryPage.js
      const updatedServer = { ...server };
      if (isSelectingMainSubnet) {
        // Changing main subnet - only update main_ip, keep additional_ips unchanged
        updatedServer.main_ip = subnet.cidr;
        console.log(`✏️ UI Update: Setting main_ip to ${subnet.cidr}, keeping additional_ips as: ${updatedServer.additional_ips}`);
      } else {
        // Adding additional subnet - only update additional_ips, keep main_ip unchanged
        const currentIps = updatedServer.additional_ips
          ? updatedServer.additional_ips.split(',').map(ip => ip.trim())
          : [];
        if (!currentIps.includes(subnet.cidr)) {
          currentIps.push(subnet.cidr);
          updatedServer.additional_ips = currentIps.join(', ');
          console.log(`✏️ UI Update: Adding to additional_ips: ${updatedServer.additional_ips}, keeping main_ip as: ${updatedServer.main_ip}`);
        }
      }
      
      // Force UI update
      setServer(updatedServer);
      
      // Show success message immediately
      alert(isSelectingMainSubnet ? '🎯 Main subnet changed! Previous main subnet will be unallocated, new subnet set as main IP, additional subnets remain unchanged. Switch configuration will run in background.' : '➕ Additional subnet added! Switch configuration and ACL creation will run in background.');
      
      // Close the modal
      setIsSubnetSelectionModalOpen(false);
      
      const token = localStorage.getItem('admin_token');
      
      // If we're assigning a new main subnet and there was a previous main subnet, unallocate it first
      if (isSelectingMainSubnet && previousMainSubnet && previousMainSubnet !== subnet.cidr) {
        console.log(`🔄 Unallocating previous main subnet: ${previousMainSubnet} (subnet swap - skip promotion)`);
        
        // Fire-and-forget unallocation call for the previous main subnet (with timeout)
        const abortController = new AbortController();
        setTimeout(() => abortController.abort(), 10000); // 10 second timeout
        
        fetch(`${API_URL}/api_admin_subnets.php?f=unallocate_subnet_server_side`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            token,
            subnet_cidr: previousMainSubnet,
            skip_promotion: true  // Skip promotion since we're swapping, not just unallocating
          }),
          signal: abortController.signal
        })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            console.log('✅ Previous main subnet unallocated successfully in background');
          } else {
            console.error('❌ Previous main subnet unallocation failed:', data.error);
          }
        })
        .catch(error => {
          console.error('❌ Previous main subnet unallocation network error:', error);
        });
      }
      
      // Send request to assign new subnet (background, no await)
      const subnetId = typeof subnet.id === 'string' && subnet.id.startsWith('SUB-')
        ? subnet.id : subnet.id;

      const assignmentPayload = {
        token,
        subnet_id: subnetId,
        server_id: serverId,
        server_type: serverType,
        set_as_main_ip: isSelectingMainSubnet
      };
      
      console.log(`📡 API Call: assign_subnet_to_server with payload:`, assignmentPayload);

      // Simple retry mechanism for assignment (more critical operation)
      const attemptAssignment = async (retries = 2) => {
        try {
          const response = await fetch(`${API_URL}/api_admin_subnets.php?f=assign_subnet_to_server`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(assignmentPayload)
          });
          
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
          }
          
          const data = await response.json();
          
          if (data.success) {
            console.log('✅ New subnet assigned successfully in background');
            console.log('🔧 Switch configuration and ACL creation handled automatically by assign_subnet_to_server API');
          } else {
            console.error('❌ Background assignment failed:', data.error);
            // Only show alert for API errors (not network errors)
            alert(`Background assignment failed: ${data.error}`);
          }
        } catch (error) {
          console.error(`❌ Network error on assignment attempt (${3-retries}/3):`, error);
          
          if (retries > 0) {
            console.log(`🔄 Retrying assignment in 1 second... (${retries} attempts left)`);
            setTimeout(() => attemptAssignment(retries - 1), 1000);
          } else {
            console.error('❌ All assignment attempts failed - operation completed optimistically in UI');
            // Don't show alert - user already sees optimistic UI update
          }
        }
      };
      
      // Start the assignment with retries
      attemptAssignment();

    } catch (err) {
      console.error('Error:', err);
      alert(`Error: ${err.message}`);
    }
  };

  // Fetch server data
// Replace the existing fetchServer function with this improved version
const fetchServer = async () => {
  try {
    setLoading(true);
    setError(null);

    const token = localStorage.getItem('admin_token');

    // Determine which endpoint to use based on server type
    const endpoint = serverType === 'dedicated'
      ? 'get_inventory_dedicated'
      : 'get_blade_server_inventory';

    const response = await fetch(`${API_URL}/api_admin_inventory.php?f=${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token: token
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response text:', errorText);
      throw new Error(`HTTP error ${response.status}: ${errorText}`);
    }

    const data = await response.json();

    if (Array.isArray(data)) {
      // Find the specific server by ID
      const foundServer = data.find(item => String(item.id) === String(serverId));

      if (foundServer) {
        setServer(foundServer);
        setLastUpdated(`${new Date().getHours()}:${String(new Date().getMinutes()).padStart(2, '0')}`);
      } else {
        throw new Error(`Server with ID ${serverId} not found`);
      }
    } else {
      throw new Error('Invalid data format returned from API');
    }

    setLoading(false);
  } catch (err) {
    console.error("Error fetching server:", err);
    setError(`Failed to load server: ${err.message}`);
    setLoading(false);
  }
};
  // Load all required data
  const loadAllData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Fetch server data
      await fetchServer();

      // Fetch reference data in parallel
      await Promise.all([
        fetchRacks(),
        fetchCities(),
        fetchCountries(),
        fetchCpuModels(),
        fetchRamConfigurations(),
        fetchChassis()
      ]);

      setLoading(false);
    } catch (err) {
      console.error("Error loading data:", err);
      setError("Failed to load server data. Please try again.");
      setLoading(false);
    }
  };

  // Effect to load data when component mounts
  useEffect(() => {
    loadAllData();
  }, [serverId, serverType]);

  // NEW: Effect to query PXE installation status (must run on every render path)
  useEffect(() => {
    checkReinstallStatus();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [serverId]);

  // Fetch racks
  const fetchRacks = async () => {
    try {
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_racks`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (Array.isArray(data)) {
        setRacks(data);
      }
    } catch (err) {
      console.error("Error fetching racks:", err);
      setRacks([]);
    }
  };

  // Fetch cities
  const fetchCities = async () => {
    try {
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_cities`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (Array.isArray(data)) {
        setCities(data);
      }
    } catch (err) {
      console.error("Error fetching cities:", err);
    }
  };

  // Fetch countries
  const fetchCountries = async () => {
    try {
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_countries`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (Array.isArray(data)) {
        setCountries(data);
      }
    } catch (err) {
      console.error("Error fetching countries:", err);
    }
  };

  // Fetch CPU models
  const fetchCpuModels = async () => {
    try {
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_dedicated_cpus`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (Array.isArray(data)) {
        setCpuModels(data);
      } else {
        setCpuModels([]);
      }
    } catch (err) {
      console.error("Error fetching CPU models:", err);
      setCpuModels([]);
    }
  };

  // Fetch RAM configurations
  const fetchRamConfigurations = async () => {
    try {
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_ram_configurations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (Array.isArray(data)) {
        setRamConfigurations(data);
      } else {
        setRamConfigurations([]);
      }
    } catch (err) {
      console.error("Error fetching RAM configurations:", err);
      setRamConfigurations([]);
    }
  };
  const handleRemoveAdditionalSubnet = (indexToRemove) => {
    if (!server || !server.additional_ips) return;

    // Get current IPs as an array
    const ips = server.additional_ips.split(',').map(ip => ip.trim());

    // Remove the specified IP
    const updatedIps = ips.filter((_, index) => index !== indexToRemove);

    // Update server state
    const updatedServer = { ...server };
    updatedServer.additional_ips = updatedIps.join(', ');
    setServer(updatedServer);
  };

  // Handle subnet unallocation - integrated from InventoryPage.js
  const handleUnallocateSubnet = async (subnetCidr, isMainSubnet) => {
    console.log('🔄 handleUnallocateSubnet called with:', { subnetCidr, isMainSubnet });
    
    // Prevent multiple simultaneous calls
    if (window.unallocationInProgress) {
      console.log('⚠️ Unallocation already in progress, ignoring click');
      return;
    }
    
    try {
      window.unallocationInProgress = true;
      
      // Confirm before unallocating
      const confirmUnallocate = window.confirm(`Are you sure you want to unallocate the subnet ${subnetCidr}?`);
      if (!confirmUnallocate) {
        console.log('❌ User cancelled unallocation');
        window.unallocationInProgress = false;
        return;
      }
      
      console.log('🚀 Starting unallocation process...');
      
      // Update UI immediately (optimistic) - same pattern as allocation
      const updatedServer = { ...server };
      if (isMainSubnet) {
        // When unallocating main subnet, check if we have additional subnets
        const additionalIps = updatedServer.additional_ips 
          ? updatedServer.additional_ips.split(',').map(ip => ip.trim()).filter(ip => ip)
          : [];
        
        if (additionalIps.length > 0) {
          // Promote the first additional subnet to main
          updatedServer.main_ip = additionalIps[0];
          // Remove the promoted subnet from additional IPs
          const remainingIps = additionalIps.slice(1);
          updatedServer.additional_ips = remainingIps.length > 0 ? remainingIps.join(', ') : '';
        } else {
          // No additional subnets, just clear main IP
          updatedServer.main_ip = null;
        }
      } else {
        // Remove from additional IPs
        const additionalIps = updatedServer.additional_ips 
          ? updatedServer.additional_ips.split(',').map(ip => ip.trim())
          : [];
        const filteredIps = additionalIps.filter(ip => ip !== subnetCidr);
        updatedServer.additional_ips = filteredIps.length > 0 ? filteredIps.join(', ') : '';
      }
      
      // Force UI update with new reference - same pattern as allocation
      setServer(updatedServer);
      console.log('✅ Updated UI immediately (optimistic)');
      
      // Show success message immediately
      alert('✅ Subnet unallocated successfully! Switch unconfiguration and ACL removal will continue in background.');
      
      const token = localStorage.getItem('admin_token');
      if (!token) {
        throw new Error('No authentication token found');
      }
      
      // Make the API call in background (same pattern as allocation)
      console.log(`📡 Making API call in background for regular unallocation: ${subnetCidr} (promotion enabled)`);
      
      const abortController = new AbortController();
      setTimeout(() => abortController.abort(), 10000); // 10 second timeout
      
      fetch(`${API_URL}/api_admin_subnets.php?f=unallocate_subnet_server_side`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          token,
          subnet_cidr: subnetCidr
          // Note: skip_promotion not passed, so defaults to false (promotion enabled)
        }),
        signal: abortController.signal
      })
      .then(response => response.json())
      .then(data => {
        console.log('📡 Background API response:', data);
        if (data.success) {
          console.log('✅ Subnet unallocated successfully in background');
        } else {
          console.error('❌ Background unallocation failed:', data.error);
          // Don't show alert for background failures, just log them
        }
      })
      .catch(error => {
        console.error('❌ Background unallocation network error:', error);
        // Don't show alert for background operations to avoid blocking user
      });

    } catch (err) {
      console.error('❌ Error in handleUnallocateSubnet:', err);
      alert(`❌ Error: ${err.message}`);
    } finally {
      window.unallocationInProgress = false;
      console.log('🏁 Unallocation process completed');
    }
  };
  // Fetch chassis (for blade servers)
  const fetchChassis = async () => {
    try {
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=get_inventory_chassis`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const data = await response.json();

      if (Array.isArray(data)) {
        setChassis(data);
      }
    } catch (err) {
      console.error("Error fetching chassis:", err);
    }
  };

  // Update server handler
  const handleUpdateServer = async () => {
    try {
      if (!server || !server.id) {
        alert('Invalid server data');
        return;
      }

      setLoading(true);
      const token = localStorage.getItem('admin_token');

      const endpoint = serverType === 'dedicated'
        ? 'update_dedicated_server'
        : 'update_blade_server_inventory';

      // Exclude IP fields from server update as they should only be managed through subnet allocation
      const { main_ip, additional_ips, ...serverDataWithoutIps } = server;

      const apiData = {
        token: token,
        ...serverDataWithoutIps
      };

      console.log(`Sending to ${endpoint}:`, JSON.stringify(apiData, null, 2));

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(apiData)
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        // Handle IP allocation after successful server update
        const ipAllocationWarnings = [];

        // Handle IPMI IP allocation for servers (only if not empty)
        if (server.ipmi && server.ipmi.trim()) {
          console.log(`Allocating IPMI IP ${server.ipmi} for ${serverType} server ${server.id} (${server.label})`);
          const ipAllocated = await allocateIp(server.ipmi, 'server', server.id, server.label || 'Unknown');
          if (!ipAllocated) {
            console.warn(`Failed to allocate IPMI IP ${server.ipmi}, but server update was successful`);
            ipAllocationWarnings.push(`IPMI IP ${server.ipmi}`);
          } else {
            console.log(`Successfully allocated IPMI IP ${server.ipmi} for server ${server.id}`);
          }
        }

        // Note: Main IP allocation is now handled exclusively through the subnet allocation system
        // and should not be processed during regular server updates

        // Turn off edit mode
        setIsEditMode(false);

        // Refresh data from server to ensure we have the latest
        fetchServer();

        setLastUpdated(`${new Date().getHours()}:${String(new Date().getMinutes()).padStart(2, '0')}`);
        
        // Show appropriate success message
        if (ipAllocationWarnings.length > 0) {
          alert(`Server updated successfully, but failed to allocate the following IPs: ${ipAllocationWarnings.join(', ')}`);
        } else {
          alert('Server updated successfully');
        }
      } else {
        alert(result.error || `Failed to update ${serverType} server`);
      }

      setLoading(false);
    } catch (err) {
      setLoading(false);
      console.error(`Error updating ${serverType} server:`, err);
      alert(`Failed to update server: ${err.message}`);
    }
  };

  // IP allocation and deallocation functions
  const allocateIp = async (ipAddress, deviceType, deviceId, deviceLabel) => {
    if (!ipAddress) return false;

    console.log(`Allocating ${ipAddress} for ${deviceType} ${deviceId || 'new'} (${deviceLabel})`);

    try {
      const token = localStorage.getItem('admin_token');

      // 1) Discover the subnet that contains this IP
      const subnetsResp = await fetch(`${API_URL}/api_admin_subnets.php?f=get_subnets`, {
        method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ token })
      });
      const allSubnets = await subnetsResp.json();
      if (!Array.isArray(allSubnets)) {
        console.error('Failed to fetch subnets list');
        return false;
      }

      let subnetId = null;
      for (const s of allSubnets) {
        const sid = typeof s.id === 'string' && s.id.startsWith('SUB-') ? s.id.replace('SUB-','') : s.id;
        try {
          const ipResp = await fetch(`${API_URL}/api_admin_subnets.php?f=get_subnet_ips`, {
            method: 'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify({ token, subnet_id: sid })
          });
          const ipData = await ipResp.json();
          if (ipData && ipData.success && ipData.ips.some(ip=>ip.ip_address===ipAddress)) { subnetId = sid; break; }
        } catch { /* ignore */ }
      }
      if (!subnetId) { console.error('Subnet for IP not found'); return false; }

      // 2) Pre-emptively free the IP
      await fetch(`${API_URL}/api_admin_subnets.php?f=force_deallocate_ip`, {
        method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify({ token, ip_address: ipAddress, for_server_ipmi: deviceType==='server', for_switch: deviceType==='switch' })
      });

      // 3) Build common payload
      const manual_alocation = deviceId?.toString()||'unknown';
      const notes = deviceType==='server' ? `IPMI for ${deviceLabel} (ID: ${deviceId})` : `Switch ${deviceLabel} (ID: ${deviceId})`;
      const payload = { token, subnet_id: subnetId, ip_address: ipAddress, manual_alocation, notes, for_server_ipmi: deviceType==='server', for_switch: deviceType==='switch', server_id: deviceType==='server'?deviceId:undefined, switch_id: deviceType==='switch'?deviceId:undefined };
      if (deviceType==='server') payload.server_type = serverType==='blade'?'blade':'dedicated';

      // 4) attempt allocate
      const allocRes = await fetch(`${API_URL}/api_admin_subnets.php?f=allocate_ip`, { method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify(payload) });
      let allocJson = await allocRes.json();
      if (!allocJson.success) {
        console.warn('allocate_ip failed, trying force_allocate_ip');
        const forcePayload = {...payload}; delete forcePayload.subnet_id;
        const forceRes = await fetch(`${API_URL}/api_admin_subnets.php?f=force_allocate_ip`, { method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify(forcePayload) });
        allocJson = await forceRes.json();
      }

      if (allocJson.success) {
        console.log('IP allocated & metadata stored');
        return true;
      }
      console.error('IP allocation failed completely:', allocJson.error || 'unknown');
      return false;

    } catch (err) {
      console.error('allocateIp threw', err); return false;
    }
  };

  const deallocateIp = async (ipAddress, deviceType, deviceId) => {
    if (!ipAddress) {
      console.error('Missing IP address for deallocation');
      return false;
    }

    try {
      const token = localStorage.getItem('admin_token');

      const requestData = {
        token: token,
        ip_address: ipAddress,
        for_server_ipmi: deviceType === 'server',
        for_switch: deviceType === 'switch'
      };

      console.log('Deallocating IP:', requestData);

      const response = await fetch(`${API_URL}/api_admin_subnets.php?f=force_deallocate_ip`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        console.log(`Successfully deallocated IP ${ipAddress}`);
        return true;
      } else {
        console.error(`Failed to deallocate IP ${ipAddress}:`, result.error);
        return false;
      }
    } catch (error) {
      console.error(`Error deallocating IP ${ipAddress}:`, error);
      return false;
    }
  };



  // Handler for editing field values
  const handleEditServerChange = async (e) => {
    const { name, value } = e.target;
    const oldValue = server[name];

    // Handle IP address changes - deallocate old IP if changing or clearing
    if ((name === 'ipmi' || name === 'main_ip') && oldValue && oldValue !== value && server.id) {
      console.log(`${name} changed from ${oldValue} to ${value || 'empty'}, deallocating old IP`);
      await deallocateIp(oldValue, 'server', server.id);
      
      // Clear device_type when changing IPMI
      if (name === 'ipmi') {
        console.log('IPMI changed, clearing device_type field');
      }
    }

    // Create a copy of the server
    const updatedServer = { ...server };

    // For numeric fields that should be numbers, convert string to number
    if (['size_ur', 'total_ports', 'rack_id', 'city_id', 'country_id'].includes(name) && value !== '') {
      updatedServer[name] = Number(value);
    } else {
      updatedServer[name] = value;
    }

    // Update the server state
    setServer(updatedServer);
  };

  // Add a new CPU model
  const handleAddCpuModel = async () => {
    try {
      if (!newCpuModel.trim()) {
        alert('CPU model name is required');
        return;
      }

      setLoading(true);
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=add_cpu_model`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          cpu: newCpuModel
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        setShowAddCpuModal(false);
        setNewCpuModel('');

        // Refresh CPU models
        await fetchCpuModels();

        alert('CPU model added successfully');
      } else {
        alert(result.error || 'Failed to add CPU model');
      }

      setLoading(false);
    } catch (err) {
      setLoading(false);
      console.error("Error adding CPU model:", err);
      alert('Failed to add CPU model: ' + err.message);
    }
  };

  // Add a new RAM configuration
  const handleAddRamConfig = async () => {
    try {
      if (!newRamConfig.size || !newRamConfig.description) {
        alert('RAM size and description are required');
        return;
      }

      setLoading(true);
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=add_ram_configuration`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          size: newRamConfig.size,
          description: newRamConfig.description
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        setShowAddRamModal(false);
        setNewRamConfig({ size: '', description: '' });

        // Refresh RAM configurations
        await fetchRamConfigurations();

        alert('RAM configuration added successfully');
      } else {
        alert(result.error || 'Failed to add RAM configuration');
      }

      setLoading(false);
    } catch (err) {
      setLoading(false);
      console.error("Error adding RAM configuration:", err);
      alert('Failed to add RAM configuration: ' + err.message);
    }
  };

  // Update MAC address for a server
  const handleUpdateMac = async (macAddress) => {
    try {
      if (!macAddress || !server || !server.id) {
        alert('Invalid MAC address or server');
        return;
      }

      console.log(`Updating MAC address to: ${macAddress} for ${serverType} server ID: ${server.id}`);

      // Create the updates object
      const updatePayload = {
        id: server.id,
        mac: macAddress
      };

      // Use the correct API endpoint based on server type
      const endpoint = serverType === 'dedicated'
        ? 'update_dedicated_server'
        : 'update_blade_server_inventory';

      setLoading(true);
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          ...updatePayload
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("API response error:", errorText);
        throw new Error(`HTTP error ${response.status}: ${errorText}`);
      }

      const result = await response.json();
      console.log("API response:", result);

      if (result.success) {
        // Update the UI state with the new MAC address
        const updatedServer = { ...server, mac: macAddress };

        // Update server state
        setServer(updatedServer);

        // Force UI refresh
        setLastUpdated(`${new Date().getHours()}:${String(new Date().getMinutes()).padStart(2, '0')}`);
        alert('MAC address updated successfully');
      } else {
        // Handle error
        let errorMessage = 'Failed to update MAC address';
        if (result.error) {
          errorMessage += `: ${result.error}`;
        }
        if (result.message) {
          errorMessage += ` (${result.message})`;
        }
        alert(errorMessage);
      }

      setLoading(false);
    } catch (err) {
      setLoading(false);
      console.error("Error updating MAC address:", err);
      alert(`Failed to update MAC address: ${err.message}`);
    }
  };

  // Update storage configuration
  const handleUpdateStorage = async (bayUpdates) => {
    try {
      if (!bayUpdates || Object.keys(bayUpdates).length === 0) {
        alert('No storage changes to apply');
        return;
      }

      if (!bayUpdates.id) {
        if (!server || !server.id) {
          alert('No server selected');
          return;
        }
        bayUpdates.id = server.id;
      }

      // Get server type (correctly handle it as a parameter rather than a field to update)
      const serverTypeParam = bayUpdates.serverType || serverType;
      delete bayUpdates.serverType; // Remove it so it doesn't get sent as a field to update

      console.log(`Updating storage configuration for ${serverTypeParam} server ID:`, bayUpdates.id);

      // Determine the maximum number of bays based on server type
      const maxBays = serverTypeParam === 'dedicated' ? 26 : 10;

      // Create the updates object with bay fields
      const updatePayload = { id: bayUpdates.id };
      let hasAnyChanges = false;

      // Filter only bay fields and check if there are any actual changes
      for (const [key, value] of Object.entries(bayUpdates)) {
        if (key.startsWith('bay') && key !== 'id') {
          updatePayload[key] = value;
          hasAnyChanges = true;
        }
      }

      if (!hasAnyChanges) {
        console.log("No storage changes to update");
        alert("No storage changes detected");
        return;
      }

      // WORKAROUND: The API doesn't include bay fields in its allowed fields list
      // For dedicated servers, we need to include an allowed field to prevent SQL error
      if (serverTypeParam === 'dedicated') {
        // Include a dummy field update that's in the API's allowed list
        // This ensures the SQL query won't have an empty SET clause
        updatePayload.notes = server.notes || '';
      }

      console.log("Final update payload:", updatePayload);

      // Use the correct API endpoint based on server type
      const endpoint = serverTypeParam === 'dedicated'
        ? 'update_dedicated_server'
        : 'update_blade_server_inventory';

      setLoading(true);
      const token = localStorage.getItem('admin_token');

      const response = await fetch(`${API_URL}/api_admin_inventory.php?f=${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          token: token,
          ...updatePayload
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("API response error:", errorText);
        throw new Error(`HTTP error ${response.status}: ${errorText}`);
      }

      const result = await response.json();
      console.log("API response:", result);

      if (result.success) {
        // Update the UI state with the new configuration
        const updatedServer = { ...server };

        // Update all bay fields
        for (const [key, value] of Object.entries(bayUpdates)) {
          if (key.startsWith('bay') && key !== 'id') {
            updatedServer[key] = value;

            // Add bay name properties for UI display
            if (value && value !== '0') {
              const storageConfig = storageConfigurations.find(config =>
                String(config.id) === String(value)
              );
              if (storageConfig) {
                updatedServer[`${key}_name`] = storageConfig.label;
              }
            } else {
              updatedServer[`${key}_name`] = '';
            }
          }
        }

        // Remove the notes timestamp if we added it as a workaround
        if (serverTypeParam === 'dedicated' && server.notes !== updatePayload.notes) {
          // Restore the original notes
          updatedServer.notes = server.notes;
        }

        // Update server state
        setServer(updatedServer);

        // Force UI refresh
        setLastUpdated(`${new Date().getHours()}:${String(new Date().getMinutes()).padStart(2, '0')}`);
        alert("Storage configuration updated successfully!");
      } else {
        // Handle error
        let errorMessage = 'Failed to update storage configuration';
        if (result.error) {
          errorMessage += `: ${result.error}`;
        }
        if (result.message) {
          errorMessage += ` (${result.message})`;
        }
        alert(errorMessage);
      }

      setLoading(false);
    } catch (err) {
      setLoading(false);
      console.error("Error updating storage configuration:", err);
      alert(`Failed to update storage: ${err.message}`);
    }
  };

  // Toggle password visibility
  const togglePasswordVisibility = (field) => {
    setVisiblePasswords(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  // Go back to inventory list
  const handleBackToInventory = () => {
    navigateTo('/admin/inventory');
  };

  // Password Field Component
  const PasswordField = ({
    isEditMode,
    fieldName,
    value,
    onChange,
    placeholder = "••••••••",
    label
  }) => {
    const isVisible = visiblePasswords[fieldName] || false;

    return (
      <div>
        <div className="text-xs text-gray-500">{label}</div>
        {isEditMode ? (
          <div className="relative">
            <input
              type={isVisible ? "text" : "password"}
              name={fieldName}
              value={value || ''}
              onChange={(e) => {
                onChange(e);
              }}
              className="font-medium w-full px-2 py-0.5 pr-8 border border-gray-300 rounded-md text-sm"
              placeholder={placeholder}
            />
            <button
              type="button"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-indigo-700"
              onClick={(e) => {
                e.preventDefault();
                togglePasswordVisibility(fieldName);
              }}
            >
              {isVisible ? (
                <Eye className="w-4 h-4" />
              ) : (
                <EyeOff className="w-4 h-4" />
              )}
            </button>
          </div>
        ) : (
          <div className="font-medium flex items-center">
            <Lock className="w-4 h-4 mr-1.5 text-indigo-700" />
            {value ? (
              <div className="flex items-center">
                <span>{isVisible ? value : placeholder}</span>
                <button
                  type="button"
                  className="ml-2 text-gray-500 hover:text-indigo-700"
                  onClick={() => togglePasswordVisibility(fieldName)}
                >
                  {isVisible ? (
                    <Eye className="w-4 h-4" />
                  ) : (
                    <EyeOff className="w-4 h-4" />
                  )}
                </button>
              </div>
            ) : (
              'Not set'
            )}
          </div>
        )}
      </div>
    );
  };

  // Render status badge
  const renderStatusBadge = (status) => {
    const badgeClasses = {
      'Available': 'bg-green-100 text-green-800',
      'Active': 'bg-green-100 text-green-800', // Keep for backward compatibility
      'In use': 'bg-yellow-100 text-yellow-800',
      'Defect': 'bg-red-100 text-red-800'
    };

    const icons = {
      'Available': <CheckCircle className="w-4 h-4 mr-1" />,
      'Active': <CheckCircle className="w-4 h-4 mr-1" />, // Keep for backward compatibility
      'In use': <Activity className="w-4 h-4 mr-1" />,
      'Defect': <AlertTriangle className="w-4 h-4 mr-1" />
    };

    return (
      <span className={`px-2 py-0.5 rounded-full text-xs font-medium flex items-center w-fit ${badgeClasses[status] || 'bg-gray-100 text-gray-800'}`}>
        {icons[status] || <AlertTriangle className="w-4 h-4 mr-1" />}
        {status}
      </span>
    );
  };

  // Legacy IpmiSelector component replaced with CountryIpSelector

  // Component for IPMI Access
  const IpmiAccessField = ({ ipmiAddress, rootPassword, isEditMode }) => {
    const [copyStatus, setCopyStatus] = useState('');

    const handleIpmiClick = () => {
      if (!ipmiAddress) return;

      // Copy root password to clipboard if available
      if (rootPassword) {
        navigator.clipboard.writeText(rootPassword)
          .then(() => {
            setCopyStatus('copied');
            // Reset status after 3 seconds
            setTimeout(() => setCopyStatus(''), 3000);
          })
          .catch(err => {
            console.error('Failed to copy password: ', err);
            setCopyStatus('error');
            // Reset status after 3 seconds
            setTimeout(() => setCopyStatus(''), 3000);
          });
      }

      // Open IPMI in new tab
      const ipmiUrl = ipmiAddress.startsWith('http')
        ? ipmiAddress
        : `http://${ipmiAddress}`;
      window.open(ipmiUrl, '_blank');
    };

    if (isEditMode) {
      return null; // Don't render in edit mode
    }

    return (
      <div className="mt-1">
        {ipmiAddress && (
          <button
            onClick={handleIpmiClick}
            className="flex items-center px-2 py-0.5 bg-indigo-100 hover:bg-indigo-200 text-indigo-700 rounded text-xs font-medium transition-colors"
          >
            <Eye className="w-3 h-3 mr-1" />
            Open IPMI Interface
            {rootPassword && (
              <span className="ml-1">
                {copyStatus === 'copied' ? (
                  <span className="text-green-600 ml-1">(Password copied!)</span>
                ) : copyStatus === 'error' ? (
                  <span className="text-red-600 ml-1">(Copy failed)</span>
                ) : (
                  <span className="text-gray-500 ml-1">(will copy root password)</span>
                )}
              </span>
            )}
          </button>
        )}
      </div>
    );
  };

  // Component for launching iDRAC Console
  const IdracConsoleLauncher = ({ ipmiAddress, username, password, version }) => {
    const [launching, setLaunching] = useState(false);
    const [showInstructions, setShowInstructions] = useState(false);

    // Generate JNLP file content for iDRAC
    const generateJnlpContent = () => {
      // Base URL for iDRAC
      const baseUrl = ipmiAddress.startsWith('http')
        ? ipmiAddress.replace(/^https?:\/\//, '')
        : ipmiAddress;

      // Different JNLP configuration based on iDRAC version
      if (version === 9) {
        return `<?xml version="1.0" encoding="UTF-8"?>
<jnlp spec="1.0+" codebase="https://${baseUrl}:443">
  <information>
    <title>Virtual Console Client</title>
    <vendor>Dell Inc.</vendor>
    <description>Virtual Console Client for iDRAC9</description>
  </information>
  <application-desc main-class="com.avocent.idrac.kvm.Main">
    <argument>IP=${baseUrl}</argument>
    <argument>JNLPSTR=JViewer</argument>
    <argument>JNLPNAME=JViewer.jnlp</argument>
    ${username ? `<argument>user=${username}</argument>` : ''}
    ${password ? `<argument>passwd=${password}</argument>` : ''}
    <argument>kmport=5900</argument>
    <argument>vport=5900</argument>
    <argument>apcp=1</argument>
    <argument>version=2</argument>
  </application-desc>
  <security>
    <all-permissions/>
  </security>
  <resources>
    <j2se version="1.8+" initial-heap-size="512m" max-heap-size="1024m"/>
    <jar href="https://${baseUrl}:443/software/avctKVM.jar" download="eager" main="true"/>
  </resources>
  <resources os="Windows" arch="x86">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOWin32.jar" download="eager"/>
  </resources>
  <resources os="Windows" arch="amd64">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOWin64.jar" download="eager"/>
  </resources>
  <resources os="Windows" arch="x86_64">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOWin64.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="x86">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="i386">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="i586">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="i686">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="amd64">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux64.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="x86_64">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux64.jar" download="eager"/>
  </resources>
  <resources os="Mac OS X" arch="x86_64">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOMac64.jar" download="eager"/>
  </resources>
</jnlp>`;
      } else {
        // Default to iDRAC 8 format
        return `<?xml version="1.0" encoding="UTF-8"?>
<jnlp spec="1.0+" codebase="https://${baseUrl}:443">
  <information>
    <title>iDRAC8 Virtual Console Client</title>
    <vendor>Dell Inc.</vendor>
    <description>iDRAC8 Virtual Console Client</description>
  </information>
  <application-desc main-class="com.avocent.idrac.kvm.Main">
    <argument>ip=${baseUrl}</argument>
    ${username ? `<argument>user=${username}</argument>` : ''}
    ${password ? `<argument>passwd=${password}</argument>` : ''}
    <argument>kmport=5900</argument>
    <argument>vport=5900</argument>
    <argument>title=iDRAC8 Virtual Console Client</argument>
    <argument>helpurl=https://${baseUrl}:443/help/contents.html</argument>
  </application-desc>
  <security>
    <all-permissions/>
  </security>
  <resources>
    <j2se version="1.6.0_24+" initial-heap-size="512m" max-heap-size="1024m"/>
    <jar href="https://${baseUrl}:443/software/avctKVM.jar" download="eager" main="true"/>
  </resources>
  <resources os="Windows" arch="x86">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOWin32.jar" download="eager"/>
  </resources>
  <resources os="Windows" arch="amd64">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOWin64.jar" download="eager"/>
  </resources>
  <resources os="Windows" arch="x86_64">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOWin64.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="x86">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="i386">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="i586">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="i686">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux32.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="amd64">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux64.jar" download="eager"/>
  </resources>
  <resources os="Linux" arch="x86_64">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOLinux64.jar" download="eager"/>
  </resources>
  <resources os="Mac OS X" arch="x86_64">
    <nativelib href="https://${baseUrl}:443/software/avctKVMIOMac64.jar" download="eager"/>
  </resources>
</jnlp>`;
      }
    };

    // Download JNLP file function
    const downloadJnlpFile = () => {
      setLaunching(true);

      try {
        const jnlpContent = generateJnlpContent();
        const blob = new Blob([jnlpContent], { type: 'application/x-java-jnlp-file' });
        const url = URL.createObjectURL(blob);

        // Create temporary link element to download the file
        const link = document.createElement('a');
        link.href = url;
        link.download = `idrac${version}-console.jnlp`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        setTimeout(() => {
          URL.revokeObjectURL(url);
          setLaunching(false);
        }, 1000);
      } catch (error) {
        console.error('Error generating JNLP file:', error);
        setLaunching(false);
      }
    };

    if (!ipmiAddress) return null;

    return (
      <div className="mt-1">
        <div className="flex flex-wrap items-center gap-2">
          <button
            onClick={downloadJnlpFile}
            disabled={launching}
            className={`flex items-center px-2 py-1 text-xs font-medium rounded transition-colors
              ${launching ? 'bg-gray-200 text-gray-500' : 'bg-blue-100 hover:bg-blue-200 text-blue-700'}`}
          >
            <svg className="w-3 h-3 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 17L15 17" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              <path d="M12 6V13" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              <path d="M12 13L15 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              <path d="M12 13L9 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              <path d="M3 15V16C3 18.2091 4.79086 20 7 20H17C19.2091 20 21 18.2091 21 16V15" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
            </svg>
            {launching ? 'Generating...' : `Launch Java Console`}
          </button>

          <button
            onClick={() => setShowInstructions(!showInstructions)}
            className="flex items-center px-2 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded text-xs transition-colors"
          >
            <svg className="w-3 h-3 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
              <path d="M12 7V13" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
              <circle cx="12" cy="17" r="1" fill="currentColor"/>
            </svg>
            {showInstructions ? 'Hide Help' : 'Help'}
          </button>
        </div>

        {showInstructions && (
          <div className="mt-2 p-3 bg-gray-50 rounded-md text-xs">
            <h4 className="font-medium mb-1">How to use iDRAC {version} Java Console:</h4>
            <ol className="list-decimal pl-4 space-y-1">
              <li>Download and install Java if you haven't already
                (<a href="https://www.java.com/download/" target="_blank" rel="noopener noreferrer"
                   className="text-blue-600 hover:underline">www.java.com</a>)</li>
              <li>Save the .jnlp file when prompted</li>
              <li>Right-click the downloaded file and select "Open with" → "Java Web Start"</li>
              <li>If prompted with security warnings, click "Run" to continue</li>
              <li>The iDRAC console should load with your credentials auto-filled</li>
            </ol>
            <p className="mt-2 text-gray-600">Note: You may need to add the iDRAC IP to the Java security exception site list in your Java Control Panel.</p>
          </div>
        )}
      </div>
    );
  };

  // Component for iDRAC auto-detection
  const IdracAutoDetectConsole = ({ ipmiAddress, username, password, selectedItem }) => {
    const [idracVersion, setIdracVersion] = useState(null);
    const [detecting, setDetecting] = useState(true);

    React.useEffect(() => {
      // Auto-detect iDRAC version when component mounts
      if (ipmiAddress) {
        setDetecting(true);

        // Try to extract version from label if available
        const detectedFromLabel = selectedItem?.label?.toLowerCase().match(/idrac\s*(\d+)/i);
        if (detectedFromLabel && (detectedFromLabel[1] === '8' || detectedFromLabel[1] === '9')) {
          setIdracVersion(parseInt(detectedFromLabel[1]));
          setDetecting(false);
          return;
        }

        // Try to detect from notes
        const detectedFromNotes = selectedItem?.notes?.toLowerCase().match(/idrac\s*(\d+)/i);
        if (detectedFromNotes && (detectedFromNotes[1] === '8' || detectedFromNotes[1] === '9')) {
          setIdracVersion(parseInt(detectedFromNotes[1]));
          setDetecting(false);
          return;
        }

        // Try to detect from model information (Dell servers with specific generations)
        const modelInfo = [
          { pattern: /r[0-9]40/i, version: 9 },    // R740, R640, R440, etc. = iDRAC 9
          { pattern: /r[0-9]30/i, version: 8 },    // R730, R630, R430, etc. = iDRAC 8
          { pattern: /r[0-9]20/i, version: 7 },    // R720, R620, R520, etc. = iDRAC 7
          { pattern: /poweredge\s*1[4-9]/i, version: 9 },  // PowerEdge 14G+ = iDRAC 9
          { pattern: /poweredge\s*1[23]/i, version: 8 },   // PowerEdge 12G-13G = iDRAC 8
          { pattern: /g1[4-9]/i, version: 9 },     // Dell G14+ servers = iDRAC 9
          { pattern: /g1[23]/i, version: 8 },      // Dell G12-G13 servers = iDRAC 8
        ];

        // Check against server label and notes
        const serverInfo = (selectedItem?.label || '') + ' ' + (selectedItem?.cpu || '') + ' ' + (selectedItem?.notes || '');

        for (const model of modelInfo) {
          if (model.pattern.test(serverInfo)) {
            setIdracVersion(model.version);
            setDetecting(false);
            return;
          }
        }

        // Default to iDRAC 8 if no detection method succeeds
        setIdracVersion(8);
        setDetecting(false);
      }
    }, [ipmiAddress, selectedItem]);

    if (!ipmiAddress) return null;

    // Only show for iDRAC 8 or 9 (iDRAC 7 and earlier used different mechanisms)
    if (idracVersion !== 8 && idracVersion !== 9) return null;

    return (
      <div className="mt-2 border-t pt-2">
        <div className="text-xs text-gray-500 mb-1">Launch iDRAC Console</div>

        {detecting ? (
          <div className="flex items-center text-xs text-gray-500">
            <RefreshCw className="w-3 h-3 animate-spin mr-1" />
            Detecting iDRAC version...
          </div>
        ) : (
          <IdracConsoleLauncher
            ipmiAddress={ipmiAddress}
            username={username}
            password={password}
            version={idracVersion}
          />
        )}
      </div>
    );
  };

  // Get country flag emoji
  const getCountryFlag = (countryName) => {
    if (!countryName) return '';

    // Map of country names to ISO country codes
    const countryCodeMap = {
      'Albania': 'AL',
      'Andorra': 'AD',
      'Austria': 'AT',
      'Belarus': 'BY',
      'Belgium': 'BE',
      'Bosnia and Herzegovina': 'BA',
      'Bulgaria': 'BG',
      'Croatia': 'HR',
      'Cyprus': 'CY',
      'Czech Republic': 'CZ',
      'Denmark': 'DK',
      'Estonia': 'EE',
      'Finland': 'FI',
      'France': 'FR',
      'Germany': 'DE',
      'Greece': 'GR',
      'Hungary': 'HU',
      'Iceland': 'IS',
      'Ireland': 'IE',
      'Italy': 'IT',
      'Latvia': 'LV',
      'Liechtenstein': 'LI',
      'Lithuania': 'LT',
      'Luxembourg': 'LU',
      'Malta': 'MT',
      'Moldova': 'MD',
      'Monaco': 'MC',
      'Montenegro': 'ME',
      'Netherlands': 'NL',
      'North Macedonia': 'MK',
      'Norway': 'NO',
      'Poland': 'PL',
      'Portugal': 'PT',
      'Romania': 'RO',
      'Russia': 'RU',
      'San Marino': 'SM',
      'Serbia': 'RS',
      'Slovakia': 'SK',
      'Slovenia': 'SI',
      'Spain': 'ES',
      'Sweden': 'SE',
      'Switzerland': 'CH',
      'Ukraine': 'UA',
      'United Kingdom': 'GB',
      'UK': 'GB',
      'United States': 'US',
      'USA': 'US',
      'Canada': 'CA',
      'Australia': 'AU',
      'Japan': 'JP',
      'China': 'CN',
      'Singapore': 'SG',
      'South Korea': 'KR',
      'India': 'IN',
      'Brazil': 'BR',
      'Mexico': 'MX',
      'South Africa': 'ZA'
    };

    // Get the country code
    const countryCode = countryCodeMap[countryName] || '';

    if (!countryCode) return '';

    // Convert country code to regional indicator symbols (flag emoji)
    const flagEmoji = countryCode
      .toUpperCase()
      .split('')
      .map(char => String.fromCodePoint(char.charCodeAt(0) + 127397))
      .join('');

    return flagEmoji;
  };

  // Loading state
  if (loading && !server) {
    return (
      <div className="flex h-screen bg-gray-100">
        <Sidebar
          sidebarCollapsed={sidebarCollapsed}
          activeMenu="Inventory"
          navigateTo={navigateTo}
          toggleSidebar={toggleSidebar}
        />
        <div className="flex-1 flex flex-col">
          <TopMenu toggleSidebar={toggleSidebar} />
          <div className="p-4 flex items-center justify-center h-full">
            <div className="text-center">
              <RefreshCw className="w-12 h-12 animate-spin mx-auto text-indigo-700 mb-2" />
              <p className="text-lg text-gray-600">Loading server details...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex h-screen bg-gray-100">
        <Sidebar
          sidebarCollapsed={sidebarCollapsed}
          activeMenu="Inventory"
          navigateTo={navigateTo}
          toggleSidebar={toggleSidebar}
        />
        <div className="flex-1 flex flex-col">
          <TopMenu toggleSidebar={toggleSidebar} />
          <div className="p-4 flex items-center justify-center h-full">
            <div className="text-center max-w-md">
              <div className="bg-red-100 p-3 rounded-lg mb-3">
                <AlertTriangle className="w-12 h-12 text-red-600 mx-auto mb-2" />
                <p className="text-lg text-red-700 font-medium">{error}</p>
              </div>
              <button
                onClick={handleBackToInventory}
                className="mt-3 bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-1.5 rounded-md text-sm font-medium transition-colors"
              >
                Back to Inventory
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Utility: Check if a PXE installation is already running for this server
  async function checkReinstallStatus() {
    try {
      if (!serverId) return;
      setCheckingInstallStatus(true);
      const token = localStorage.getItem('admin_token');
      const response = await fetch(`${API_URL}/pxe_api_integration.php?f=check_installation_status`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ token, server_id: serverId })
      });

      if (!response.ok) {
        // If the endpoint fails we assume no installation is running (fail-open)
        setInstallationInProgress(false);
        return;
      }
      const data = await response.json();
      setInstallationInProgress(!!data.installation_in_progress);
    } catch (err) {
      console.error('Failed to check reinstall status:', err);
      setInstallationInProgress(false);
    } finally {
      setCheckingInstallStatus(false);
    }
  }

  return (
    <div className="flex h-screen bg-gray-100">
      <Sidebar
        sidebarCollapsed={sidebarCollapsed}
        activeMenu="Inventory"
        navigateTo={navigateTo}
        toggleSidebar={toggleSidebar}
      />
      <div className="flex-1 flex flex-col bg-gray-100">
        <TopMenu toggleSidebar={toggleSidebar} />

        <div className="p-3 space-y-3 overflow-auto">
          {/* Header with back button */}
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <button
                onClick={handleBackToInventory}
                className="mr-3 bg-white p-2 rounded-full shadow-sm hover:bg-gray-50"
              >
                <ChevronLeft className="w-5 h-5 text-gray-600" />
              </button>
              <div>
                <h1 className="text-xl font-bold text-gray-800">
                  {server?.label || `${serverType.charAt(0).toUpperCase() + serverType.slice(1)} Server Details`}
                </h1>
                <div className="flex items-center text-sm text-gray-500">
                  <span className="mr-2">ID: {server?.id}</span>
                  <span className="mx-2">•</span>
                  {serverType === 'dedicated' ? (
                    <span className="flex items-center">
                      <Server className="w-4 h-4 mr-1" />
                      Dedicated Server
                    </span>
                  ) : (
                    <span className="flex items-center">
                      <Layers className="w-4 h-4 mr-1" />
                      Blade Server
                    </span>
                  )}
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <div className="text-sm text-gray-500">Last updated: {lastUpdated}</div>
              <button
                onClick={() => loadAllData()}
                className="p-1.5 text-gray-500 hover:text-indigo-700 rounded-full hover:bg-gray-100"
                disabled={loading}
              >
                <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              </button>
              {!isEditMode ? (
                <button
                  className="px-3 py-1 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md flex items-center text-sm transition-colors"
                  onClick={() => setIsEditMode(true)}
                >
                  <Edit className="w-4 h-4 mr-1" />
                  Edit
                </button>

              ) : (
                <>

                  <button
                    className="px-3 py-1 border border-gray-300 rounded-md text-gray-700 flex items-center text-sm hover:bg-gray-50"
                    onClick={() => {
                      setIsEditMode(false);
                      // Reload server to reset any changes
                      fetchServer();
                    }}
                  >
                    <X className="w-4 h-4 mr-1" />
                    Cancel
                  </button>
                  <button
                    className="px-3 py-1 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md flex items-center text-sm transition-colors"
                    onClick={handleUpdateServer}
                    disabled={loading}
                  >
                    <Save className="w-4 h-4 mr-1" />
                    Save
                  </button>
                </>
              )}
            </div>
          </div>
            {/* Power Management Card */}
            {activeTab === 'details' && (
  <div className="space-y-3">
    <PowerManagement
      server={server}
      serverType={serverType}
      ipmiAddress={server?.ipmi}
      ipmiRootPassword={server?.ipmi_root_pass}
      onRefresh={() => fetchServer()}
    />
  </div>
)}
          {/* Status and client info */}
          <div className="bg-white rounded-lg shadow-sm p-3 flex justify-between items-center">
            <div className="flex items-center">
              {isEditMode ? (
                <select
                  name="status"
                  value={server?.status || 'Available'}
                  onChange={handleEditServerChange}
                  className="px-2 py-0.5 border border-gray-300 rounded-md text-sm"
                >
                          <option value="Available">Available</option>
                          <option value="In Use">In Use</option>
                          <option value="Defect">Defect</option>
                </select>
              ) : (
                renderStatusBadge(server?.status || 'Available')
              )}
            </div>

            {server?.order_id && (
              <div className="text-right">
                <a
                  href={`/admin/orders/${server.order_id}`}
                  className="text-indigo-700 hover:underline flex items-center justify-end"
                  onClick={(e) => {
                    e.preventDefault();
                    navigateTo(`/admin/orders/${server.order_id}`);
                  }}
                >
                  <Calendar className="w-4 h-4 mr-1" />
                  Order #{server.order_id}
                </a>
                {server.client_id && (
                  <a
                    href={`/admin/accounts/${server.client_id}`}
                    className="text-gray-600 hover:underline flex items-center justify-end mt-0.5"
                    onClick={(e) => {
                      e.preventDefault();
                      navigateTo(`/admin/accounts/${server.client_id}`);
                    }}
                  >
                    <User className="w-4 h-4 mr-1" />
                    {server.company_name || server.client_name}
                  </a>
                )}
              </div>


            )}

          </div>


          {/* Tab Navigation */}
<div className="bg-white rounded-lg shadow-sm p-1 flex justify-between items-center border-b mt-3">
  <div className="flex">
    <button
      onClick={() => setActiveTab('details')}
      className={`px-4 py-2 text-sm font-medium rounded-t-md ${
        activeTab === 'details'
          ? 'text-indigo-700 border-b-2 border-indigo-700'
          : 'text-gray-600 hover:text-indigo-700'
      }`}
    >
      <Server className="w-4 h-4 inline mr-1" />
      Server Details
    </button>
    <button
      onClick={() => setActiveTab('idrac')}
      className={`px-4 py-2 text-sm font-medium rounded-t-md ${
        activeTab === 'idrac'
          ? 'text-indigo-700 border-b-2 border-indigo-700'
          : 'text-gray-600 hover:text-indigo-700'
      }`}
    >
      <Settings className="w-4 h-4 inline mr-1" />
      iDRAC Monitoring
    </button>
    <button
      onClick={() => setActiveTab('logs')}
      className={`px-4 py-2 text-sm font-medium rounded-t-md ${
        activeTab === 'logs'
          ? 'text-indigo-700 border-b-2 border-indigo-700'
          : 'text-gray-600 hover:text-indigo-700'
      }`}
    >
      <Database className="w-4 h-4 inline mr-1" />
      iDRAC Logs
    </button>
  </div>
  
  {/* PXE Reinstall Button */}
  <button
    onClick={() => setIsPXEReinstallModalOpen(true)}
    disabled={!server?.ipmi || !server?.ipmi_root_pass || installationInProgress || checkingInstallStatus}
    className={`px-3 py-1 rounded text-xs font-medium flex items-center mr-2 ${
      !server?.ipmi || !server?.ipmi_root_pass || installationInProgress || checkingInstallStatus
        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
        : 'bg-orange-100 text-orange-800 hover:bg-orange-200'
    }`}
    title={
      !server?.ipmi || !server?.ipmi_root_pass
        ? 'IPMI credentials required'
        : installationInProgress
          ? 'A PXE installation is already in progress for this server'
          : 'Reinstall server via PXE'
    }
  >
    <Terminal className="w-3 h-3 mr-1" />
    {checkingInstallStatus ? 'Checking…' : 'PXE Reinstall'}
  </button>
</div>
{activeTab === 'details' && (
 <>
          {/* Main Content */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-3">
            {/* Basic Info Card */}
            <div className="bg-white rounded-lg shadow-sm p-3">
              <h4 className="text-sm font-semibold text-gray-800 mb-2 border-b pb-1">Basic Information</h4>
              <div className="space-y-2">
                <div>
                  <div className="text-xs text-gray-500">Label</div>
                  {isEditMode ? (
                    <input
                      type="text"
                      name="label"
                      value={server?.label || ''}
                      onChange={handleEditServerChange}
                      className="font-medium w-full px-2 py-0.5 border border-gray-300 rounded-md text-sm"
                    />
                  ) : (
                    <div className="font-medium">{server?.label}</div>
                  )}
                </div>


                <div>
                  <div className="text-xs text-gray-500">CPU</div>
                  {isEditMode ? (
                    <div className="flex">
                      <select
                        name="cpu"
                        value={server?.cpu || ''}
                        onChange={handleEditServerChange}
                        className="font-medium w-full px-2 py-0.5 border border-gray-300 rounded-md text-sm"
                      >
                        <option value="">Select CPU Model</option>
                        {cpuModels.map(cpu => (
                          <option key={cpu.id} value={cpu.id}>{cpu.cpu}</option>
                        ))}
                      </select>
                      <button
                        type="button"
                        onClick={() => setShowAddCpuModal(true)}
                        className="ml-2 p-0.5 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center"
                        title="Add New CPU Model"
                      >
                        <Plus className="w-4 h-4" />
                      </button>
                    </div>
                  ) : (
                    <div className="font-medium flex items-center">
                      <Cpu className="w-4 h-4 mr-1.5 text-indigo-700" />
                      {server?.cpu_name || server?.cpu || 'Not specified'}
                    </div>
                  )}
                </div>

                <div>
                  <div className="text-xs text-gray-500">RAM</div>
                  {isEditMode ? (
                    <div className="flex">
                      <select
                        name="ram"
                        value={server?.ram || ''}
                        onChange={handleEditServerChange}
                        className="font-medium w-full px-2 py-0.5 border border-gray-300 rounded-md text-sm"
                      >
                        <option value="">Select RAM Configuration</option>
                        {ramConfigurations.map(ram => (
                          <option key={ram.id} value={ram.id}>{ram.description}</option>
                        ))}
                      </select>
                      <button
                        type="button"
                        onClick={() => setShowAddRamModal(true)}
                        className="ml-2 p-0.5 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center"
                        title="Add New RAM Configuration"
                      >
                        <Plus className="w-4 h-4" />
                      </button>
                    </div>
                  ) : (
                    <div className="font-medium">
                      <Server className="w-4 h-4 mr-1.5 text-indigo-700 inline" />
                      {server?.ram_description ||
                       (server?.ram ? `${server.ram}GB` : 'Not specified')}
                    </div>
                  )}
                </div>

                <PasswordField
                  isEditMode={isEditMode}
                  fieldName="password"
                  value={server?.password || ''}
                  onChange={handleEditServerChange}
                  placeholder="••••••••"
                  label="Server Password"
                />
                {serverType === 'dedicated'&& (
                 <div className="mb-3">
                    <div className="text-xs text-gray-500">Size U</div>
                    {isEditMode ? (
                      <input
                        type="text"
                        name="size"
                        value={server?.size || ''}
                        onChange={handleEditServerChange}
                        className="font-medium w-full px-2 py-1 border border-gray-300 rounded-md text-sm"
                        placeholder="e.g. ***********"
                      />
                    ) : (
                      <div className="font-medium flex items-center">

                        {server?.size || 'Not assigned'}
                      </div>
                    )}
                  </div>
)}
{/* Main Subnet Address */}
<div>
  <div className="text-xs text-gray-500">Main Subnet</div>
  <div className="font-medium flex items-center">
    {server?.main_ip || 'Not assigned'}
          <div className="flex items-center ml-2">
      {server?.main_ip && (
        <button
          type="button"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            handleUnallocateSubnet(server.main_ip, true);
          }}
          className="p-1 bg-red-100 text-red-700 rounded-md hover:bg-red-200 flex items-center justify-center"
          title="Unallocate subnet"
        >
          <XCircle className="w-4 h-4" />
        </button>
      )}
      <button
        type="button"
        onClick={() => {
          setIsSelectingMainSubnet(true);
          setIsSubnetSelectionModalOpen(true);
        }}
        className="p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center mr-1"
        title="Select from available subnets"
        style={{marginLeft:'12px'}}
      >
        <Network className="w-4 h-4" />
      </button>
      </div>
  </div>
</div>

{/* Additional Subnets Field */}
<div>
  <div className="text-xs text-gray-500">Additional Subnets</div>
  <div className="font-medium">
    {server?.additional_ips ? (
      <div className="flex flex-wrap gap-1 items-center">
        {server.additional_ips.split(',').map((ip, index) => {
          const trimmedIp = ip.trim();
          return trimmedIp ? (
            <div key={index} className="flex items-center">
                              <span className="text-sm px-2 py-0.5 bg-gray-100 rounded inline-block mr-1">
                  {trimmedIp}
                </span>
                <button
                  type="button"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleUnallocateSubnet(trimmedIp, false);
                  }}
                  className="p-1 bg-red-100 text-red-700 rounded-md hover:bg-red-200 flex items-center justify-center"
                  title="Unallocate subnet"
                >
                  <XCircle className="w-4 h-4" />
                </button>
             </div>
           ) : null;
         }).filter(Boolean)}
         <button
           type="button"
           onClick={() => {
             setIsSelectingMainSubnet(false);
             setIsSubnetSelectionModalOpen(true);
           }}
           className="ml-2 p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center"
           title="Add subnet from available subnets"
         >
           <Network className="w-4 h-4" />
         </button>
      </div>
    ) : (
      <div className="flex flex-wrap gap-1">
        <span className="text-gray-500">None assigned </span>
        <button
          type="button"
          onClick={() => {
            setIsSelectingMainSubnet(false);
            setIsSubnetSelectionModalOpen(true);
          }}
          className="ml-2 p-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 flex items-center justify-center"
          title="Add subnet from available subnets"
        >
          <Network className="w-4 h-4" />
        </button>
      </div>
    )}
  </div>
</div>


              </div>
            </div>

            {/* Location Card */}
            <div className="bg-white rounded-lg shadow-sm p-3">
              <h4 className="text-sm font-semibold text-gray-800 mb-2 border-b pb-1">Location Information</h4>
              <div className="space-y-2">
                <div>
                  <div className="text-xs text-gray-500">Datacenter</div>
                  {isEditMode && (serverType === 'dedicated') ? (
                    <select
                      name="city_id"
                      value={server?.city_id || ''}
                      onChange={handleEditServerChange}
                      className="font-medium w-full px-2 py-0.5 border border-gray-300 rounded-md text-sm"
                    >
                      <option value="">Select City/Datacenter</option>
                      {cities.map(city => (
                        <option key={city.id} value={city.id}>{city.city} {city.datacenter ? `(${city.datacenter})` : ''}</option>
                      ))}
                    </select>
                  ) : (
                    <div className="font-medium flex items-center">
                      <Building className="w-4 h-4 mr-1.5 text-indigo-700" />
                      {server?.city_name} {server?.datacenter ? `(${server.datacenter})` : ''}
                      {isEditMode && serverType === 'blade' && (
                        <span className="ml-2 text-xs text-gray-500 italic">(Location set by chassis)</span>
                      )}
                    </div>
                  )}
                </div>

                <div>
                  <div className="text-xs text-gray-500">Country</div>
                  {isEditMode && (serverType === 'dedicated') ? (
                    <select
                      name="country_id"
                      value={server?.country_id || ''}
                      onChange={handleEditServerChange}
                      className="font-medium w-full px-2 py-0.5 border border-gray-300 rounded-md text-sm"
                    >
                      <option value="">Select Country</option>
                      {countries.map(country => (
                        <option key={country.id} value={country.id}>{country.country}</option>
                      ))}
                    </select>
                  ) : (
                    <div className="font-medium flex items-center">
                      <Globe className="w-4 h-4 mr-1.5 text-indigo-700" />
                      {server?.country_name} {getCountryFlag(server?.country_name)}
                      {isEditMode && serverType === 'blade' && (
                        <span className="ml-2 text-xs text-gray-500 italic">(Country set by chassis)</span>
                      )}
                    </div>
                  )}
                </div>

                <div>
                  <div className="text-xs text-gray-500">Rack</div>
                  {isEditMode && (serverType === 'dedicated') ? (
                    <div className="flex gap-2">
                      <select
                        name="rack_id"
                        value={server?.rack_id || ''}
                        onChange={handleEditServerChange}
                        className="font-medium w-2/3 px-2 py-0.5 border border-gray-300 rounded-md text-sm"
                      >
                        <option value="">Select Rack</option>
                        {racks.map(rack => (
                          <option key={rack.id} value={rack.id}>{rack.rack_name}</option>
                        ))}
                      </select>
                      <input
                        type="text"
                        name="position"
                        value={server?.position || ''}
                        onChange={handleEditServerChange}
                        className="font-medium w-1/3 px-2 py-0.5 border border-gray-300 rounded-md text-sm"
                        placeholder="Position"
                      />
                    </div>
                  ) : (
                    <div className="font-medium flex items-center">
                      <Server className="w-4 h-4 mr-1.5 text-indigo-700" />
                      {server?.rack_name}
                      {server?.position ? `(${server.position})` : ''}
                      {isEditMode && serverType === 'blade' && (
                        <span className="ml-2 text-xs text-gray-500 italic">(Rack set by chassis)</span>
                      )}
                    </div>
                  )}
                </div>

                {serverType === 'blade' && (
                  <div>
                    <div className="text-xs text-gray-500">Chassis</div>
                    {isEditMode ? (
                      <select
                        name="chassis_id"
                        value={server?.chassis_id || ''}
                        onChange={handleEditServerChange}
                        className="font-medium w-full px-2 py-0.5 border border-gray-300 rounded-md text-sm"
                      >
                        <option value="">Select Chassis</option>
                        {chassis.map(c => (
                          <option key={c.id} value={c.id}>{c.label}</option>
                        ))}
                      </select>
                    ) : (
                      <div className="font-medium flex items-center">
                        <HardDrive className="w-4 h-4 mr-1.5 text-indigo-700" />
                        {chassis.find(c => c.id === server?.chassis_id)?.label || 'Not assigned'}
                      </div>
                    )}
                  </div>
                )}

                <div>
                  <div className="text-xs text-gray-500">IPMI</div>
                  {isEditMode ? (
                    <CountryIpSelector
                      selectedItem={server}
                      onChange={handleEditServerChange}
                      name="ipmi"
                      value={server?.ipmi || ''}
                      label="IPMI IP Address"
                      placeholder="IPMI Address"
                      serverType={server?.server_type || 'dedicated'}
                    />
                  ) : (
                    <div className="font-medium">
                      <div className="flex items-center">
                        <Cpu className="w-4 h-4 mr-1.5 text-indigo-700" />
                        {server?.ipmi || 'Not specified'}
                      </div>

                      {/* IPMI interface access button */}
                      <IpmiAccessField
                        ipmiAddress={server?.ipmi}
                        rootPassword={server?.ipmi_root_pass}
                        isEditMode={isEditMode}
                      />

                      {/* Enhanced iDRAC Console selector with auto-detection */}
                      {server?.ipmi && (
                        <IdracAutoDetectConsole
                          ipmiAddress={server.ipmi}
                          username="root"
                          password={server.ipmi_root_pass}
                          selectedItem={server}
                        />
                      )}
                    </div>
                  )}
                </div>

                <PasswordField
                  isEditMode={isEditMode}
                  fieldName="ipmi_root_pass"
                  value={server?.ipmi_root_pass || ''}
                  onChange={handleEditServerChange}
                  placeholder="••••••••"
                  label="IPMI Root Password"
                />

                <PasswordField
                  isEditMode={isEditMode}
                  fieldName="ipmi_user_pass"
                  value={server?.ipmi_user_pass || ''}
                  onChange={handleEditServerChange}
                  placeholder="••••••••"
                  label="IPMI User Password"
                />
              </div>
            </div>

            {/* Network Card */}

{/* Network Card */}
<div className="bg-white rounded-lg shadow-sm p-3">
  <h4 className="text-sm font-semibold text-gray-800 mb-2 border-b pb-1">Network Information</h4>
  <div className="space-y-3">
    {/* NetworkConnections Component */}
    <NetworkConnections
      server={server}
      serverType={serverType}
      onRefresh={() => {
        // Refresh the server data when network changes
        fetchServer();
      }}
      editable={true} // Allow port assignment in both view and edit modes
      isEditMode={isEditMode} // Pass the edit mode state to control delete button visibility
    />




    <div>
      <div className="text-xs text-gray-500">MAC Address</div>
      {isEditMode ? (
        <input
          type="text"
          name="mac"
          value={server?.mac || ''}
          onChange={handleEditServerChange}
          className="font-medium w-full px-2 py-0.5 border border-gray-300 rounded-md text-sm"
        />
      ) : (
        <div>
          <div className="font-medium flex items-center">
            <Network className="w-4 h-4 mr-1.5 text-indigo-700" />
            {server?.mac || 'Not specified'}
          </div>

          {/* MAC Address Detection */}
          <MacAddressDetectionButton
            selectedItem={server}
            onUpdateMac={handleUpdateMac}
            isEditMode={isEditMode}
            serverType={serverType}
          />
        </div>
      )}
    </div>
  </div>
</div>



          </div>

          {/* Storage/Bay Configuration and Notes Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-3">
            {/* Storage Configuration for Dedicated Servers */}
            {serverType === 'dedicated' && (
              <div className="bg-white rounded-lg shadow-sm p-3">
                <h4 className="text-sm font-semibold text-gray-800 mb-2 border-b pb-1">Storage Configuration</h4>

                {/* Storage Detection Button */}
                <StorageDetectionButton
                  selectedItem={server}
                  onUpdateStorage={handleUpdateStorage}
                  isEditMode={isEditMode}
                  serverType="dedicated"
                />

                {/* Display bays in a grid */}
                <div className="mt-2">
                  <div className="grid grid-cols-2 sm:grid-cols-4 gap-2">
                    {Array.from({ length: 26 }, (_, i) => i + 1).map(bayNum => {
                      const bayKey = `bay${bayNum}`;
                      const bayNameKey = `${bayKey}_name`;

                      // Check if bay exists and is not '0'
                      const bayValue = server?.[bayKey];
                      const bayName = server?.[bayNameKey] || (
                        bayValue && storageConfigurations.find(config => String(config.id) === String(bayValue))?.label
                      );

                      if (!isEditMode) {
                        return bayValue && bayValue !== '0' ? (
                          <div key={bayNum} className="bg-gray-50 p-1.5 rounded">
                            <div className="text-xs text-gray-500">Bay {bayNum}</div>
                            <div className="font-medium text-xs flex items-center">
                              <HardDrive className="w-3 h-3 mr-1 text-indigo-700" />
                              {bayName || `Drive ${bayValue}`}
                            </div>
                          </div>
                        ) : null;
                      } else {
                        return (
                          <div key={bayNum} className="bg-gray-50 p-1.5 rounded">
                            <div className="text-xs text-gray-500">Bay {bayNum}</div>
                            <select
                              name={bayKey}
                              value={server?.[bayKey] || '0'}
                              onChange={handleEditServerChange}
                              className="font-medium w-full px-1 py-0.5 border border-gray-300 rounded-md text-xs"
                            >
                              <option value="0">Not Used</option>
                              {storageConfigurations.map(config => (
                                <option key={config.id} value={config.id}>{config.label}</option>
                              ))}
                            </select>
                          </div>
                        );
                      }
                    })}
                  </div>
                </div>
              </div>
            )}

{/* Bay configuration for blade servers */}
{serverType === 'blade' && (
  <div className="bg-white rounded-lg shadow-sm p-3">
    <h4 className="text-sm font-semibold text-gray-800 mb-2 border-b pb-1">Bay Configuration</h4>
    <StorageDetectionButton
      selectedItem={server}
      onUpdateStorage={handleUpdateStorage}
      isEditMode={isEditMode}
      serverType="blade"
    />
    <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 mt-2">
      {[1, 2, 3, 4, 5, 6, 7, 8].map(bayNum => {
        const bayKey = `bay${bayNum}`;
        const bayNameKey = `${bayKey}_name`;

        // Check if bay exists and is not '0'
        const bayValue = server?.[bayKey];

        // Find the storage configuration for this bay
        const bayConfig = storageConfigurations.find(config =>
          String(config.id) === String(bayValue)
        );

        // Use storage label if available, otherwise fall back to the custom name or ID
        const bayName = server?.[bayNameKey] || (bayConfig ? bayConfig.label : bayValue);

        if (!isEditMode) {
          return bayValue && bayValue !== '0' ? (
            <div key={bayNum} className="bg-gray-50 p-1.5 rounded">
              <div className="text-xs text-gray-500">Bay {bayNum}</div>
              <div className="font-medium text-xs flex items-center">
                <HardDrive className="w-3 h-3 mr-1 text-indigo-700" />
                {bayName || `Drive ${bayValue}`}
              </div>
            </div>
          ) : null;
        } else {
          return (
            <div key={bayNum} className="bg-gray-50 p-1.5 rounded">
              <div className="text-xs text-gray-500">Bay {bayNum}</div>
              <select
                name={bayKey}
                value={server?.[bayKey] || '0'}
                onChange={handleEditServerChange}
                className="font-medium w-full px-1 py-0.5 border border-gray-300 rounded-md text-xs"
              >
                <option value="0">Not Used</option>
                {storageConfigurations.map(config => (
                  <option key={config.id} value={config.id}>{config.label}</option>
                ))}
              </select>
            </div>
          );
        }
      })}
    </div>
  </div>
)}

            {/* Notes */}
            <div className="bg-white rounded-lg shadow-sm p-3">
              <h4 className="text-sm font-semibold text-gray-800 mb-2 border-b pb-1">Notes</h4>
              {isEditMode ? (
                <textarea
                  name="notes"
                  value={server?.notes || ''}
                  onChange={handleEditServerChange}
                  rows="3"
                  className="w-full px-2 py-0.5 border border-gray-300 rounded-md text-sm"
                ></textarea>
              ) : (
                <div className="text-sm text-gray-700 whitespace-pre-line bg-gray-50 p-2 rounded min-h-[80px]">
                  {server?.notes || 'No notes available'}
                </div>
              )}
            </div>
          </div>
            </>
          )}
                {/* iDRAC Monitoring Tab */}
{activeTab === 'idrac' && server && (
  <SensorInfoTab
    server={server}
    serverType={serverType}
    ipmiAddress={server?.ipmi}
    ipmiRootPassword={server?.ipmi_root_pass}
  />
)}

{/* Logs Tab */}
{activeTab === 'logs' && (
  <ServerLogsTab server={server} serverType={serverType} />
)}

        </div>

      </div>


      {/* Add CPU Model Modal */}
      {showAddCpuModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-4">
            <div className="flex justify-between items-center mb-3">
              <h3 className="text-lg font-bold">Add New CPU Model</h3>
              <button
                onClick={() => {
                  setShowAddCpuModal(false);
                  setNewCpuModel('');
                }}
                className="text-gray-500 hover:text-gray-700"
              >
                <XCircle className="w-5 h-5" />
              </button>
            </div>

            <div className="mb-3">
              <label className="block text-sm font-medium text-gray-700 mb-1">CPU Model Name</label>
              <input
                type="text"
                value={newCpuModel}
                onChange={(e) => setNewCpuModel(e.target.value)}
                className="w-full p-1.5 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="e.g. Intel Xeon E5-2680 v4 (14 cores, 2.4GHz)"
              />
            </div>

            <div className="flex justify-end space-x-3 pt-3 border-t">
              <button
                type="button"
                onClick={() => {
                  setShowAddCpuModal(false);
                  setNewCpuModel('');
                }}
                className="px-3 py-1.5 border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50"
                disabled={loading}
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleAddCpuModel}
                className="px-3 py-1.5 bg-indigo-700 text-white rounded-md text-sm hover:bg-indigo-800 flex items-center"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    Adding...
                  </>
                ) : (
                  <>
                    <Plus className="w-4 h-4 mr-2" />
                    Add CPU Model
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Add RAM Configuration Modal */}
      {showAddRamModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-4">
            <div className="flex justify-between items-center mb-3">
              <h3 className="text-lg font-bold">Add New RAM Configuration</h3>
              <button
                onClick={() => {
                  setShowAddRamModal(false);
                  setNewRamConfig({ size: '', description: '' });
                }}
                className="text-gray-500 hover:text-gray-700"
              >
                <XCircle className="w-5 h-5" />
              </button>
            </div>

            <div className="mb-3">
              <label className="block text-sm font-medium text-gray-700 mb-1">RAM Size (GB)</label>
              <input
                type="number"
                value={newRamConfig.size}
                onChange={(e) => setNewRamConfig({...newRamConfig, size: e.target.value})}
                className="w-full p-1.5 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="e.g. 64"
              />
            </div>

            <div className="mb-3">
              <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <input
                type="text"
                value={newRamConfig.description}
                onChange={(e) => setNewRamConfig({...newRamConfig, description: e.target.value})}
                className="w-full p-1.5 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="e.g. 64GB DDR4"
              />
            </div>

            <div className="flex justify-end space-x-3 pt-3 border-t">
              <button
                type="button"
                onClick={() => {
                  setShowAddRamModal(false);
                  setNewRamConfig({ size: '', description: '' });
                }}
                className="px-3 py-1.5 border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50"
                disabled={loading}
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleAddRamConfig}
                className="px-3 py-1.5 bg-indigo-700 text-white rounded-md text-sm hover:bg-indigo-800 flex items-center"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    Adding...
                  </>
                ) : (
                  <>
                    <Plus className="w-4 h-4 mr-2" />
                    Add RAM Configuration
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
      {/* Subnet Selection Modal */}
{isSubnetSelectionModalOpen && (
  <SelectSubnetModal
    onClose={() => setIsSubnetSelectionModalOpen(false)}
    onSelectSubnet={handleSubnetSelection}
    isMainSubnet={isSelectingMainSubnet}
    currentValue={isSelectingMainSubnet ? server?.main_ip : server?.additional_ips}
    serverType={serverType}
    serverId={server?.id}
  />
)}

      {/* PXE Reinstall Modal */}
      {isPXEReinstallModalOpen && (
        <PXEReinstallModal
          isOpen={isPXEReinstallModalOpen}
          onClose={() => setIsPXEReinstallModalOpen(false)}
          server={server}
          serverType={serverType}
          onStatusChange={(inProgress) => setInstallationInProgress(inProgress)}
        />
      )}
    </div>

  );
};

export default ServerView;