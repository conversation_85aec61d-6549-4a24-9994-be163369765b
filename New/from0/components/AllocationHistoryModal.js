import React, { useState, useEffect } from 'react';
import { XCircle, Clock, CheckCircle, AlertCircle, Server, Network, User, Calendar, Hash, Tag } from 'lucide-react';
import axios from 'axios';
import { API_URL } from '../config';

const AllocationHistoryModal = ({ serverId, serverType, onClose }) => {
  const [history, setHistory] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [limit, setLimit] = useState(10);

  useEffect(() => {
    if (serverId) {
      fetchAllocationHistory();
    }
  }, [serverId, limit]);

  const fetchAllocationHistory = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await axios.get(
        `${API_URL}/api_admin_subnets.php?f=get_allocation_history&server_id=${serverId}&limit=${limit}`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
          }
        }
      );

      if (response.data.success) {
        setHistory(response.data.history || []);
      } else {
        throw new Error(response.data.error || 'Failed to fetch allocation history');
      }
    } catch (err) {
      console.error('Error fetching allocation history:', err);
      setError(err.response?.data?.error || err.message || 'Failed to fetch allocation history');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'skipped':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status) => {
    const baseClasses = "px-2 py-1 rounded-full text-xs font-medium";
    switch (status) {
      case 'success':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'failed':
        return `${baseClasses} bg-red-100 text-red-800`;
      case 'skipped':
        return `${baseClasses} bg-yellow-100 text-yellow-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const parseAllocationDetails = (detailsJson) => {
    try {
      return JSON.parse(detailsJson);
    } catch {
      return null;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="p-6 border-b flex justify-between items-center">
          <div className="flex items-center space-x-3">
            <Server className="w-6 h-6 text-indigo-700" />
            <div>
              <h2 className="text-xl font-bold text-gray-800">Subnet Allocation History</h2>
              <p className="text-sm text-gray-600">
                Server ID: {serverId} ({serverType})
              </p>
            </div>
          </div>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <XCircle className="w-5 h-5" />
          </button>
        </div>

        {/* Controls */}
        <div className="p-4 border-b bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <label className="text-sm font-medium text-gray-700">Show last:</label>
              <select
                value={limit}
                onChange={(e) => setLimit(parseInt(e.target.value))}
                className="border border-gray-300 rounded-md px-3 py-1 text-sm"
              >
                <option value={5}>5 entries</option>
                <option value={10}>10 entries</option>
                <option value={25}>25 entries</option>
                <option value={50}>50 entries</option>
              </select>
            </div>
            <button
              onClick={fetchAllocationHistory}
              disabled={isLoading}
              className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:opacity-50"
            >
              {isLoading ? 'Loading...' : 'Refresh'}
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {error && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
              <div className="flex items-center">
                <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
                <span className="text-red-700">{error}</span>
              </div>
            </div>
          )}

          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
              <span className="ml-2 text-gray-600">Loading allocation history...</span>
            </div>
          ) : history.length === 0 ? (
            <div className="text-center py-8">
              <Network className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No allocation history found for this server.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {history.map((entry, index) => {
                const details = parseAllocationDetails(entry.allocation_details);
                return (
                  <div key={entry.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        {getStatusIcon(entry.status)}
                        <div>
                          <div className="flex items-center space-x-2">
                            <span className={getStatusBadge(entry.status)}>
                              {entry.status.toUpperCase()}
                            </span>
                            <span className="text-sm text-gray-500">
                              {entry.allocation_type === 'automatic' ? 'Auto' : 'Manual'}
                            </span>
                          </div>
                          <div className="text-sm text-gray-600 mt-1">
                            {formatDate(entry.created_at)}
                          </div>
                        </div>
                      </div>
                      {entry.subnet_cidr && (
                        <div className="text-right">
                          <div className="font-mono text-sm font-medium text-gray-800">
                            {entry.subnet_cidr}
                          </div>
                          {entry.subnet_id && (
                            <div className="text-xs text-gray-500">
                              ID: {entry.subnet_id}
                            </div>
                          )}
                        </div>
                      )}
                    </div>

                    {entry.error_message && (
                      <div className="mb-3 p-3 bg-red-50 border border-red-200 rounded-md">
                        <div className="text-sm text-red-700">
                          <strong>Error:</strong> {entry.error_message}
                        </div>
                      </div>
                    )}

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      {entry.triggered_by_username && (
                        <div className="flex items-center space-x-2">
                          <User className="w-4 h-4 text-gray-400" />
                          <span className="text-gray-600">By: {entry.triggered_by_username}</span>
                        </div>
                      )}
                      {entry.order_id && (
                        <div className="flex items-center space-x-2">
                          <Hash className="w-4 h-4 text-gray-400" />
                          <span className="text-gray-600">Order: #{entry.order_id}</span>
                        </div>
                      )}
                      {details && details.switch_config && (
                        <div className="flex items-center space-x-2">
                          <Network className="w-4 h-4 text-gray-400" />
                          <span className="text-gray-600">
                            Switch: {details.switch_config.success ? 'Configured' : 'Failed'}
                          </span>
                        </div>
                      )}
                      {details && details.allocation_type && (
                        <div className="flex items-center space-x-2">
                          <Tag className="w-4 h-4 text-gray-400" />
                          <span className="text-gray-600">Type: {details.allocation_type}</span>
                        </div>
                      )}
                    </div>

                    {details && (details.children_deleted > 0 || details.assignment_type) && (
                      <div className="mt-3 pt-3 border-t border-gray-200">
                        <div className="text-xs text-gray-500 space-y-1">
                          {details.children_deleted > 0 && (
                            <div>Deleted {details.children_deleted} child subnet(s)</div>
                          )}
                          {details.assignment_type && (
                            <div>Assignment type: {details.assignment_type}</div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t bg-gray-50">
          <div className="flex justify-end">
            <button
              onClick={onClose}
              className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AllocationHistoryModal;
