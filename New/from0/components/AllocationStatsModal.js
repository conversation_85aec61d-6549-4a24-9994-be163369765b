import React, { useState, useEffect } from 'react';
import { XCircle, BarChart3, TrendingUp, Server, CheckCircle, AlertCircle, Calendar, Hash } from 'lucide-react';
import axios from 'axios';
import { API_URL } from '../config';

const AllocationStatsModal = ({ onClose }) => {
  const [stats, setStats] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [period, setPeriod] = useState(30);

  useEffect(() => {
    fetchAllocationStats();
  }, [period]);

  const fetchAllocationStats = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await axios.get(
        `${API_URL}/api_admin_subnets.php?f=get_allocation_stats&days=${period}`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
          }
        }
      );

      if (response.data.success) {
        setStats(response.data.stats);
      } else {
        throw new Error(response.data.error || 'Failed to fetch allocation statistics');
      }
    } catch (err) {
      console.error('Error fetching allocation stats:', err);
      setError(err.response?.data?.error || err.message || 'Failed to fetch allocation statistics');
    } finally {
      setIsLoading(false);
    }
  };

  const calculateSuccessRate = () => {
    if (!stats || !stats.total_allocations || stats.total_allocations === 0) return 0;
    return Math.round((stats.successful_allocations / stats.total_allocations) * 100);
  };

  const StatCard = ({ icon: Icon, title, value, subtitle, color = "indigo" }) => {
    const colorClasses = {
      indigo: "bg-indigo-50 text-indigo-700 border-indigo-200",
      green: "bg-green-50 text-green-700 border-green-200",
      red: "bg-red-50 text-red-700 border-red-200",
      yellow: "bg-yellow-50 text-yellow-700 border-yellow-200",
      blue: "bg-blue-50 text-blue-700 border-blue-200",
      purple: "bg-purple-50 text-purple-700 border-purple-200"
    };

    return (
      <div className={`p-4 rounded-lg border ${colorClasses[color]}`}>
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium opacity-75">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
            {subtitle && <p className="text-xs opacity-60 mt-1">{subtitle}</p>}
          </div>
          <Icon className="w-8 h-8 opacity-60" />
        </div>
      </div>
    );
  };

  const ProgressBar = ({ value, max, color = "indigo" }) => {
    const percentage = max > 0 ? (value / max) * 100 : 0;
    const colorClasses = {
      indigo: "bg-indigo-600",
      green: "bg-green-600",
      red: "bg-red-600",
      yellow: "bg-yellow-600"
    };

    return (
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className={`h-2 rounded-full ${colorClasses[color]}`}
          style={{ width: `${Math.min(percentage, 100)}%` }}
        ></div>
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="p-6 border-b flex justify-between items-center">
          <div className="flex items-center space-x-3">
            <BarChart3 className="w-6 h-6 text-indigo-700" />
            <div>
              <h2 className="text-xl font-bold text-gray-800">Subnet Allocation Statistics</h2>
              <p className="text-sm text-gray-600">
                Automatic allocation performance metrics
              </p>
            </div>
          </div>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <XCircle className="w-5 h-5" />
          </button>
        </div>

        {/* Controls */}
        <div className="p-4 border-b bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <label className="text-sm font-medium text-gray-700">Time period:</label>
              <select
                value={period}
                onChange={(e) => setPeriod(parseInt(e.target.value))}
                className="border border-gray-300 rounded-md px-3 py-1 text-sm"
              >
                <option value={7}>Last 7 days</option>
                <option value={30}>Last 30 days</option>
                <option value={90}>Last 90 days</option>
                <option value={365}>Last year</option>
              </select>
            </div>
            <button
              onClick={fetchAllocationStats}
              disabled={isLoading}
              className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:opacity-50"
            >
              {isLoading ? 'Loading...' : 'Refresh'}
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {error && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
              <div className="flex items-center">
                <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
                <span className="text-red-700">{error}</span>
              </div>
            </div>
          )}

          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
              <span className="ml-2 text-gray-600">Loading statistics...</span>
            </div>
          ) : stats ? (
            <div className="space-y-6">
              {/* Overview Stats */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <StatCard
                  icon={Hash}
                  title="Total Allocations"
                  value={stats.total_allocations || 0}
                  subtitle={`in last ${period} days`}
                  color="indigo"
                />
                <StatCard
                  icon={CheckCircle}
                  title="Successful"
                  value={stats.successful_allocations || 0}
                  subtitle={`${calculateSuccessRate()}% success rate`}
                  color="green"
                />
                <StatCard
                  icon={AlertCircle}
                  title="Failed"
                  value={stats.failed_allocations || 0}
                  subtitle="allocation failures"
                  color="red"
                />
                <StatCard
                  icon={Server}
                  title="Unique Servers"
                  value={stats.unique_servers || 0}
                  subtitle="servers processed"
                  color="blue"
                />
              </div>

              {/* Server Type Breakdown */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <Server className="w-5 h-5 mr-2" />
                  Server Type Breakdown
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-700">Dedicated Servers</span>
                      <span className="text-sm text-gray-600">{stats.dedicated_allocations || 0}</span>
                    </div>
                    <ProgressBar 
                      value={stats.dedicated_allocations || 0} 
                      max={stats.total_allocations || 1} 
                      color="indigo" 
                    />
                    <div className="text-xs text-gray-500 mt-1">
                      {stats.total_allocations > 0 
                        ? Math.round(((stats.dedicated_allocations || 0) / stats.total_allocations) * 100)
                        : 0}% of total allocations
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-700">Blade Servers</span>
                      <span className="text-sm text-gray-600">{stats.blade_allocations || 0}</span>
                    </div>
                    <ProgressBar 
                      value={stats.blade_allocations || 0} 
                      max={stats.total_allocations || 1} 
                      color="purple" 
                    />
                    <div className="text-xs text-gray-500 mt-1">
                      {stats.total_allocations > 0 
                        ? Math.round(((stats.blade_allocations || 0) / stats.total_allocations) * 100)
                        : 0}% of total allocations
                    </div>
                  </div>
                </div>
              </div>

              {/* Success Rate Analysis */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <TrendingUp className="w-5 h-5 mr-2" />
                  Performance Analysis
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600 mb-2">
                      {calculateSuccessRate()}%
                    </div>
                    <div className="text-sm text-gray-600">Success Rate</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-indigo-600 mb-2">
                      {stats.total_allocations > 0 
                        ? Math.round((stats.unique_servers / stats.total_allocations) * 100)
                        : 0}%
                    </div>
                    <div className="text-sm text-gray-600">Unique Server Ratio</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600 mb-2">
                      {stats.unique_servers > 0 
                        ? Math.round(stats.total_allocations / stats.unique_servers * 10) / 10
                        : 0}
                    </div>
                    <div className="text-sm text-gray-600">Avg Allocations/Server</div>
                  </div>
                </div>
              </div>

              {/* Additional Insights */}
              {stats.total_allocations === 0 && (
                <div className="text-center py-8">
                  <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">No allocation data available for the selected period.</p>
                  <p className="text-sm text-gray-500 mt-2">
                    Try selecting a longer time period or check back after some allocations have been performed.
                  </p>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8">
              <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No statistics available.</p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t bg-gray-50">
          <div className="flex justify-end">
            <button
              onClick={onClose}
              className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AllocationStatsModal;
