# Automatic Subnet Allocation System

This document describes the automatic subnet allocation system for dedicated and blade servers, including setup, usage, testing, and troubleshooting.

## Overview

The automatic subnet allocation system automatically assigns appropriate subnets to servers when orders are processed, eliminating the need for manual subnet allocation in most cases.

### Key Features

- **Automatic Selection**: Intelligently selects the most suitable available subnet based on server type and location
- **Conflict Resolution**: Automatically handles existing subnet assignments by deallocating old subnets before allocating new ones
- **Server Type Awareness**: Different allocation strategies for dedicated vs blade servers
- **Location Preference**: Prioritizes subnets in the same location as the server
- **Comprehensive Logging**: Full audit trail of all allocation activities
- **Admin Interface**: View allocation history and statistics through the web interface

## System Components

### Backend Components

1. **API Functions** (`api_admin_subnets.php`):
   - `autoAllocateSubnetToServer()` - Main allocation function
   - `findBestAvailableSubnet()` - Subnet selection algorithm
   - `deallocateServerSubnet()` - Cleanup existing allocations
   - `logSubnetAllocation()` - Logging functionality

2. **API Endpoints**:
   - `auto_allocate_subnet` - Manual subnet allocation trigger
   - `get_allocation_history` - Retrieve allocation history
   - `get_allocation_stats` - Get allocation statistics

3. **Order Integration** (`api_admin_orders.php`):
   - Automatic allocation when orders transition to 'Installing' or 'Active' status
   - Internal API calls to subnet allocation system
   - Comprehensive result reporting

### Frontend Components

1. **AllocationHistoryModal** - View detailed allocation history for servers
2. **AllocationStatsModal** - View system-wide allocation statistics
3. **SubnetsPage Integration** - Access allocation features from subnet management

### Database Components

1. **subnet_allocation_log** - Comprehensive logging table
2. **Enhanced subnets table** - Tracks server assignments
3. **Server inventory tables** - Links to dedicated/blade server inventories

## Installation and Setup

### Prerequisites

- PHP 7.4+ with PDO MySQL extension
- MySQL/MariaDB database
- Existing subnet and server inventory data
- Admin authentication system

### Database Setup

The system automatically creates the required `subnet_allocation_log` table on first use. To manually create it:

```sql
CREATE TABLE subnet_allocation_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    server_id INT NOT NULL,
    server_type ENUM('dedicated', 'blade') NOT NULL,
    subnet_id INT NULL,
    subnet_cidr VARCHAR(50) NULL,
    allocation_type ENUM('manual', 'automatic') DEFAULT 'automatic',
    status ENUM('success', 'failed', 'skipped') NOT NULL,
    error_message TEXT NULL,
    triggered_by INT NULL COMMENT 'Admin user ID who triggered the allocation',
    order_id INT NULL COMMENT 'Order ID if triggered by order processing',
    allocation_details JSON NULL COMMENT 'Full allocation result details',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_server_id (server_id),
    INDEX idx_server_type (server_type),
    INDEX idx_subnet_id (subnet_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_order_id (order_id)
);
```

### Configuration

The system uses the following configuration:

- **Server Type Detection**: Based on server ID (>= 1000000 for dedicated, < 1000000 for blade)
- **Subnet Preferences**: 
  - Dedicated servers: /29, /28, /27, /26 (in order of preference)
  - Blade servers: /30, /29, /28, /27 (in order of preference)
- **Location Matching**: Prioritizes subnets in the same city as the server

## Usage

### Automatic Allocation (Order Processing)

Automatic allocation is triggered when:
1. An order contains servers (dedicated or blade)
2. The order status changes to 'Installing' or 'Active'
3. The server doesn't already have a subnet allocated

No manual intervention is required - the system handles everything automatically.

### Manual Allocation (API)

To manually trigger allocation for a specific server:

```bash
curl -X POST "http://your-domain/api_admin_subnets.php?f=auto_allocate_subnet" \
  -H "Content-Type: application/json" \
  -d '{
    "server_id": 1000001,
    "server_type": "dedicated",
    "location_id": 1,
    "preferred_subnet_size": 29
  }'
```

### Admin Interface

1. **View Statistics**: Click the "Stats" button in the Subnets page header
2. **View Server History**: In subnet details, click "History" for assigned servers
3. **Monitor Activity**: Check allocation logs and success rates

## Testing and Validation

### System Validation (Safe for Production)

Run the validation script to check system readiness:

```bash
php validate_subnet_allocation_system.php
```

This script performs read-only checks and is safe to run in production.

### Comprehensive Testing (Test Environment Only)

**⚠️ WARNING: Only run in test environment!**

```bash
php test_automatic_subnet_allocation.php
```

This script performs actual allocations and modifications for testing purposes.

### Test Types

1. **Basic Allocation** - Tests fundamental allocation functionality
2. **Conflict Resolution** - Tests handling of existing subnet assignments
3. **Server Type Detection** - Validates dedicated vs blade server handling
4. **Location Preference** - Tests location-based subnet selection
5. **Error Handling** - Validates proper error responses
6. **Database Integrity** - Checks for data consistency issues
7. **Logging Functionality** - Verifies audit trail creation

## Troubleshooting

### Common Issues

1. **No Available Subnets**
   - Check that customer subnets exist and are marked as available
   - Verify subnet types (is_customer_subnet=1, is_public=1, is_managmenet_subnet=0)
   - Ensure subnets aren't already allocated

2. **Location Preference Not Working**
   - Verify subnet_location_map table has entries
   - Check that servers have city_id set
   - Ensure cities table is populated

3. **Allocation Failures**
   - Check error logs for specific error messages
   - Verify server exists in appropriate inventory table
   - Ensure database permissions are correct

4. **Switch Configuration Issues**
   - Check that servers have switch_id and port information
   - Verify switch configuration functions are available
   - Review switch operation logs

### Debugging

1. **Enable Verbose Logging**: Check PHP error logs for detailed allocation attempts
2. **Check Allocation History**: Use the admin interface to view allocation attempts
3. **Validate System**: Run the validation script to identify configuration issues
4. **Database Queries**: Check the subnet_allocation_log table for detailed information

### Performance Considerations

- The system uses database transactions to ensure consistency
- Subnet selection queries are optimized with proper indexing
- Location-based queries may be slower with large datasets
- Consider subnet hierarchy optimization for better performance

## API Reference

### auto_allocate_subnet

Automatically allocate a subnet to a server.

**Parameters:**
- `server_id` (required): Server ID
- `server_type` (required): 'dedicated' or 'blade'
- `location_id` (optional): Preferred location ID
- `preferred_subnet_size` (optional): Preferred subnet size
- `order_id` (optional): Order ID for logging context

**Response:**
```json
{
  "success": true,
  "subnet_id": 123,
  "subnet_cidr": "***********/29",
  "server_id": 1000001,
  "allocation_type": "automatic",
  "switch_config": {...}
}
```

### get_allocation_history

Get allocation history for a specific server.

**Parameters:**
- `server_id` (required): Server ID
- `limit` (optional): Number of entries to return (default: 10)

### get_allocation_stats

Get system-wide allocation statistics.

**Parameters:**
- `days` (optional): Number of days to analyze (default: 30)

## Security Considerations

- All API endpoints require admin authentication
- Database operations use prepared statements to prevent SQL injection
- Sensitive information is logged securely
- Transaction rollback ensures data consistency on errors

## Maintenance

### Regular Tasks

1. **Monitor Allocation Success Rate**: Check statistics regularly
2. **Review Failed Allocations**: Investigate and resolve recurring issues
3. **Clean Old Logs**: Archive or remove old allocation logs as needed
4. **Update Subnet Inventory**: Ensure adequate subnet availability

### Backup Considerations

- Include subnet_allocation_log table in regular backups
- Consider the impact of subnet assignments on server configurations
- Test restoration procedures with subnet allocation data

## Support

For issues or questions regarding the automatic subnet allocation system:

1. Check the allocation history and statistics in the admin interface
2. Run the validation script to identify configuration issues
3. Review error logs for specific error messages
4. Consult this documentation for troubleshooting steps

## Version History

- **v1.0**: Initial implementation with basic allocation functionality
- **v1.1**: Added comprehensive logging and admin interface integration
- **v1.2**: Enhanced testing and validation tools
